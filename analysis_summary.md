# 京东外卖抽奖活动接口分析总结

## 📋 文件分析概览

### 文件清单
- **request(2).txt**: 领取奖励接口 (comp_data_interact)
- **request(3).txt**: 加载活动数据接口 (comp_data_load)
- **ProxyPin6-7_10_04_14.har**: 完整网络抓包记录

## 🔍 主要差异分析

### 1. 接口功能对比

| 特性 | request(2) - 领取奖励 | request(3) - 加载数据 |
|------|---------------------|---------------------|
| **URL** | comp_data_interact | comp_data_load |
| **功能** | 执行抽奖/领取奖励 | 获取活动配置信息 |
| **fnCode** | "invoke" | 无此参数 |
| **rewardReceiveKey** | 必需(长字符串) | 无此参数 |
| **actFlowCode** | "receiveReward" | 无此参数 |

### 2. 请求参数结构差异

#### 共同参数
```
appid=day_day_reward
loginType=2
loginWQBiz=tttwxapp
```

#### 关键差异参数
- **functionId**: `comp_data_interact` vs `comp_data_load`
- **body.fnCode**: 仅在interact接口中存在
- **body.bizParams.rewardReceiveKey**: 仅在interact接口中存在
- **body.bizParams.actFlowCode**: 仅在interact接口中存在

### 3. 响应数据差异

#### request(2) 响应特点
- 包含具体奖励信息 (`rewardInfoList`)
- 优惠券详细信息 (`couponInfo`)
- 领取状态和结果反馈

#### request(3) 响应特点
- 活动基础配置 (`actBasicInfo`)
- 时间配置信息 (`instanceBeginTime`, `instanceEndTime`)
- 用户身份标签 (`identityTagMap`)
- 活动进度信息 (`rewardProgressItems`)

## 🔄 调用流程

```
1. 调用 comp_data_load 获取活动状态
   ↓
2. 从响应中提取 rewardReceiveKey
   ↓
3. 调用 comp_data_interact 执行领取
   ↓
4. 获取奖励结果
```

## 🛡️ 安全机制

### h5st参数
- 复杂的防刷验证参数
- 包含时间戳、设备指纹、签名算法
- 格式: `timestamp;algorithm_result;app_id;signature`

### 认证机制
- **Cookie认证**: sdtoken, pt_key, pt_pin等
- **Token验证**: 活动专用token
- **设备绑定**: 多个设备相关参数

## 📝 实现要点

1. **必须按顺序调用**: 先load后interact
2. **参数依赖**: rewardReceiveKey来自load接口响应
3. **时效性**: h5st参数包含时间戳，需实时生成
4. **完整性**: Cookie和header信息必须完整

## 🚀 完整脚本

已创建 `jd_lottery_complete.py` 包含:
- 完整的请求封装
- 参数解析和处理
- 错误处理机制
- 使用示例和说明

## ⚠️ 注意事项

1. **h5st算法**: 当前为示例实现，实际需要完整逆向
2. **认证信息**: Cookie和token需要从真实抓包中获取
3. **合规使用**: 请遵守相关服务条款
4. **频率控制**: 避免过于频繁的请求