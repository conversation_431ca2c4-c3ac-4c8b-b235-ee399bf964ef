# 京东外卖抽奖活动自动化脚本

基于抓包分析的完整实现，支持自动参数提取和智能错误处理。

## 🚀 功能特点

- ✅ **完整流程**: 自动加载活动数据 → 领取奖励
- ✅ **智能提取**: 自动从抓包文件提取认证信息
- ✅ **错误处理**: 完善的重试机制和异常处理
- ✅ **多种模式**: 单次抽奖、多次尝试、状态检查
- ✅ **配置管理**: 支持配置文件和交互式设置
- ✅ **详细日志**: 完整的操作日志记录

## 📋 文件说明

### 核心文件
- `jd_lottery_final.py` - 主脚本文件
- `config.json` - 配置文件(首次运行自动生成)
- `jd_lottery.log` - 运行日志(自动生成)

### 分析文件
- `jd_lottery_complete.py` - 基础版本脚本
- `analysis_summary.md` - 接口分析总结
- `comparison_result.md` - TXT与HAR文件对比
- `analyze_har.py` - HAR文件分析工具

### 抓包文件(可选)
- `request(2).txt` - 领取奖励接口抓包
- `request(3).txt` - 加载活动数据接口抓包
- `ProxyPin6-7_10_04_14.har` - 完整HAR抓包文件

## 🔧 安装和使用

### 1. 安装依赖
```bash
pip install requests
```

### 2. 准备抓包文件
将抓包文件放在脚本同目录下：
- TXT文件: `request(2).txt`, `request(3).txt`
- HAR文件: `*.har`

### 3. 运行脚本
```bash
python jd_lottery_final.py
```

### 4. 按提示操作
脚本会引导您完成设置和选择运行模式。

## 📖 使用模式

### 交互式模式
运行脚本后按提示选择：
1. **认证信息来源**
   - 手动输入
   - 从文件自动提取
   - 跳过设置

2. **运行模式**
   - 单次抽奖
   - 多次抽奖
   - 检查活动状态
   - 测试连接

### 编程模式
```python
from jd_lottery_final import JDLotteryBot

# 创建机器人
bot = JDLotteryBot()

# 设置认证信息
bot.set_auth_info(
    cookies="your_cookies_here",
    token="your_token_here",
    act_key="your_act_key_here"
)

# 或者自动提取
bot.auto_extract_from_files()

# 执行抽奖
success = bot.run_lottery()

# 多次尝试
success_count = bot.run_multiple_attempts(max_attempts=5, interval=60)
```

## 🔍 接口分析结果

### 主要差异
| 特性 | comp_data_interact | comp_data_load |
|------|-------------------|----------------|
| **功能** | 领取奖励 | 加载活动数据 |
| **fnCode** | "invoke" | 无 |
| **rewardReceiveKey** | 必需 | 无 |
| **调用顺序** | 第二步 | 第一步 |

### 调用流程
```
1. comp_data_load → 获取活动状态和rewardReceiveKey
2. comp_data_interact → 使用rewardReceiveKey领取奖励
```

## ⚙️ 配置说明

### 基础配置
```json
{
  "user_agent": "京东APP的User-Agent",
  "base_url": "https://api.m.jd.com/client.action",
  "appid": "day_day_reward",
  "request_timeout": 30,
  "retry_times": 3,
  "retry_delay": 2
}
```

### 认证信息
```json
{
  "auth_info": {
    "cookies": "从抓包中获取的Cookie字符串",
    "token": "活动Token",
    "act_key": "活动Key",
    "sdk_token": "SDK Token(可选)"
  }
}
```

## 📝 日志说明

脚本会生成详细的运行日志：
- 📄 **控制台输出**: 实时显示关键信息
- 📄 **文件日志**: 保存到 `jd_lottery.log`
- 📄 **日志级别**: INFO, WARNING, ERROR, DEBUG

## ⚠️ 重要提醒

1. **h5st算法**: 当前使用简化实现，实际使用需要完整逆向
2. **认证时效**: Cookie和Token有时效性，需要定期更新
3. **合规使用**: 请遵守相关服务条款，合理使用
4. **测试环境**: 建议先在测试环境验证功能

## 🐛 常见问题

### Q: 提示"缺少必要的认证信息"
A: 请确保设置了正确的Cookie、Token和ActKey

### Q: 请求失败或超时
A: 检查网络连接，或者更新认证信息

### Q: h5st参数错误
A: 当前使用简化算法，需要实现完整的h5st生成逻辑

### Q: 活动已结束或不存在
A: 检查ActKey是否正确，或者活动是否仍在进行

## 📞 技术支持

如有问题，请检查：
1. 日志文件 `jd_lottery.log`
2. 网络连接状态
3. 认证信息是否过期
4. 活动是否仍在进行

## 📄 许可证

本项目仅供学习和研究使用，请勿用于商业用途。