#!/usr/bin/env python3
import json
import urllib.parse

# 读取HAR文件
with open('ProxyPin6-7_10_04_14.har', 'r', encoding='utf-8') as f:
    har_data = json.load(f)

# 查找JD API请求
jd_requests = []
for entry in har_data['log']['entries']:
    url = entry['request']['url']
    if 'api.m.jd.com' in url:
        jd_requests.append(entry)

print(f"Found {len(jd_requests)} JD API requests:")
print("=" * 60)

for i, req in enumerate(jd_requests):
    url = req['request']['url']
    method = req['request']['method']

    # 解析URL参数
    parsed_url = urllib.parse.urlparse(url)
    query_params = urllib.parse.parse_qs(parsed_url.query)

    print(f"{i+1}. {method} {url}")

    if 'functionId' in query_params:
        function_id = query_params['functionId'][0]
        print(f"   functionId: {function_id}")

        # 检查是否是我们关注的接口
        if function_id in ['comp_data_interact', 'comp_data_load']:
            print(f"   *** 这是目标接口: {function_id} ***")

            # 检查POST数据
            if 'postData' in req['request']:
                post_data = req['request']['postData']
                if 'text' in post_data:
                    print(f"   POST数据长度: {len(post_data['text'])} 字符")

                    # 解析POST参数
                    try:
                        post_params = urllib.parse.parse_qs(post_data['text'])
                        if 'body' in post_params:
                            body_json = json.loads(post_params['body'][0])
                            print(f"   body参数包含: {list(body_json.keys())}")
                    except:
                        print("   无法解析POST数据")

    print()

# 详细对比目标接口
print("\n" + "=" * 80)
print("详细对比目标接口数据")
print("=" * 80)

target_requests = []
for entry in har_data['log']['entries']:
    url = entry['request']['url']
    if 'api.m.jd.com' in url and 'client.action' in url:
        parsed_url = urllib.parse.urlparse(url)
        query_params = urllib.parse.parse_qs(parsed_url.query)
        if 'functionId' in query_params:
            function_id = query_params['functionId'][0]
            if function_id in ['comp_data_interact', 'comp_data_load'] and entry['request']['method'] == 'POST':
                target_requests.append((function_id, entry))

print(f"找到 {len(target_requests)} 个目标请求")

for function_id, entry in target_requests:
    print(f"\n--- {function_id} ---")

    # 获取POST数据
    if 'postData' in entry['request'] and 'text' in entry['request']['postData']:
        post_data = entry['request']['postData']['text']
        post_params = urllib.parse.parse_qs(post_data)

        print(f"POST参数:")
        for key, value in post_params.items():
            if key == 'body':
                try:
                    body_json = json.loads(value[0])
                    print(f"  {key}: {json.dumps(body_json, indent=2, ensure_ascii=False)}")
                except:
                    print(f"  {key}: {value[0][:100]}...")
            else:
                print(f"  {key}: {value[0]}")

    # 获取响应数据
    if 'response' in entry and 'content' in entry['response'] and 'text' in entry['response']['content']:
        response_text = entry['response']['content']['text']
        try:
            response_json = json.loads(response_text)
            print(f"响应状态: {response_json.get('success', 'unknown')}")
            if 'data' in response_json:
                data_keys = list(response_json['data'].keys()) if isinstance(response_json['data'], dict) else 'not dict'
                print(f"响应数据包含: {data_keys}")
        except:
            print(f"响应数据长度: {len(response_text)} 字符")

    print("-" * 40)