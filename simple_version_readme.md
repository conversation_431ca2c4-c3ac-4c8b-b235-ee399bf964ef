# 京东外卖抽奖脚本 - 简化版本

## 🎯 特点

- ✅ **纯request实现** - 移除了复杂的配置类
- ✅ **无交互界面** - 直接运行，简单高效
- ✅ **回归正常脚本** - 标准的Python requests写法
- ✅ **基于抓包分析** - 完全按照真实接口实现

## 📁 文件说明

- `jd_lottery_simple.py` - 主脚本文件 (276行)
- `request(2).txt` - 领取奖励接口抓包
- `request(3).txt` - 加载活动数据接口抓包

## 🚀 使用方法

### 1. 安装依赖
```bash
pip install requests
```

### 2. 设置Cookie
编辑 `jd_lottery_simple.py` 文件中的 `main()` 函数，替换 `cookies` 变量为你的真实Cookie：

```python
cookies = """
你的完整Cookie字符串
"""
```

### 3. 运行脚本
```bash
python jd_lottery_simple.py
```

## 📋 代码结构

### 核心类
```python
class JDLottery:
    def __init__(self):
        # 初始化session和请求头

    def set_cookies(self, cookies):
        # 设置Cookie

    def extract_from_files(self):
        # 从抓包文件提取参数

    def load_activity_data(self):
        # 第一步：加载活动数据

    def claim_reward(self):
        # 第二步：领取奖励

    def run(self):
        # 执行完整流程
```

### 参数说明

#### 固定参数 (已内置)
- `appid`: "day_day_reward"
- `loginType`: "2"
- `loginWQBiz`: "tttwxapp"
- `sdkToken`: 从抓包分析得到的固定值
- `ubbLoc`: "ttf.lqzx"
- `lid`: "19_1601_50258_62859"

#### 动态参数 (自动获取)
- `act_token`: 从抓包文件或第一步响应获取
- `act_key`: 从抓包文件或第一步响应获取
- `reward_receive_key`: 从第一步响应获取

#### 用户提供
- `cookies`: 用户认证Cookie