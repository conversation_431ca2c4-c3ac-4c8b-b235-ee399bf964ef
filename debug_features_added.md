# 🔍 调试功能增强完成

## ✅ **调试功能增强总结**

已为脚本增加了全面的调试信息打印功能，帮助用户调试Cookie和请求相关的错误。

## 🔧 **增加的调试功能**

### **1. Cookie设置调试**

```javascript
async setCookies(cookies) {
    // 调试信息：打印Cookie设置过程
    console.log('🔍 [调试] 设置Cookie:', cookies ? cookies.substring(0, 100) + '...' : '无Cookie');
    
    // 设置用户Cookie到实例和全局$
    this.cookies = cookies;
    global.$.UserName = this.extractPtPin(cookies);
    
    console.log('🔍 [调试] 提取到用户名:', global.$.UserName || '未提取到');

    // 初始化过程...
    
    console.log('🔍 [调试] jsToken初始化结果:', this.jsToken ? this.jsToken.substring(0, 20) + '...' : '未获取到jsToken');
    
    // 设置请求头Cookie
    this.headers['Cookie'] = cookies;
    console.log('🔍 [调试] Cookie已设置到请求头');
}
```

### **2. loadActivityData请求调试**

```javascript
// 调试信息：打印请求Cookie
console.log('🔍 [调试] loadActivityData请求Cookie:', this.headers['Cookie'] ? this.headers['Cookie'].substring(0, 100) + '...' : '无Cookie');
console.log('🔍 [调试] 请求参数:', {
    actToken: this.actToken ? this.actToken.substring(0, 10) + '...' : '空',
    actKey: this.actKey ? this.actKey.substring(0, 10) + '...' : '空',
    h5st: h5st ? h5st.substring(0, 20) + '...' : '空',
    eidToken: eidToken ? eidToken.substring(0, 20) + '...' : '空'
});
```

### **3. claimReward请求调试**

```javascript
// 调试信息：打印请求Cookie
console.log('🔍 [调试] claimReward请求Cookie:', this.headers['Cookie'] ? this.headers['Cookie'].substring(0, 100) + '...' : '无Cookie');
console.log('🔍 [调试] 请求参数:', {
    actToken: this.actToken ? this.actToken.substring(0, 10) + '...' : '空',
    actKey: this.actKey ? this.actKey.substring(0, 10) + '...' : '空',
    rewardReceiveKey: this.rewardReceiveKey ? this.rewardReceiveKey.substring(0, 20) + '...' : '空',
    h5st: h5st ? h5st.substring(0, 20) + '...' : '空',
    eidToken: eidToken ? eidToken.substring(0, 20) + '...' : '空'
});
```

### **4. h5st签名调试**

```javascript
// 调试信息：打印h5st请求参数
console.log('🔍 [调试] h5st请求参数:', {
    functionId: functionId,
    appid: h5stParams.appid,
    uuid: h5stParams.uuid ? h5stParams.uuid.substring(0, 10) + '...' : '空',
    bodyLength: h5stParams.body ? h5stParams.body.length : 0
});

// h5st请求后的调试信息
if (response.data && response.data.h5st) {
    console.log('🔍 [调试] h5st签名获取成功:', response.data.h5st.substring(0, 30) + '...');
    return response.data.h5st;
} else {
    console.log('🔍 [调试] h5st签名获取失败，响应:', response.data);
}

// 异常处理调试
catch (error) {
    console.log('🔍 [调试] h5st请求异常:', error.message);
}
```

## 📊 **调试信息输出示例**

### **Cookie设置阶段**

```
🔍 [调试] 设置Cookie: pt_key=AAJjQxxx-xxx;pt_pin=jd_xxx;...
🔍 [调试] 提取到用户名: jd_xxx
🔍 [调试] jsToken初始化结果: tk01m6fb6b2c4xxx...
🔍 [调试] Cookie已设置到请求头
```

### **loadActivityData请求阶段**

```
🔍 [调试] loadActivityData请求Cookie: pt_key=AAJjQxxx-xxx;pt_pin=jd_xxx;...
🔍 [调试] 请求参数: {
  actToken: "pVQSJCqPp3...",
  actKey: "iadem13cpd...",
  h5st: "20250103154523123...",
  eidToken: "tk01m6fb6b2c4xxx..."
}
```

### **h5st签名阶段**

```
🔍 [调试] h5st请求参数: {
  functionId: "comp_data_load",
  appid: "day_day_reward",
  uuid: "12345678-1...",
  bodyLength: 245
}
🔍 [调试] h5st签名获取成功: 20250103154523123456789012...
```

### **claimReward请求阶段**

```
🔍 [调试] claimReward请求Cookie: pt_key=AAJjQxxx-xxx;pt_pin=jd_xxx;...
🔍 [调试] 请求参数: {
  actToken: "pVQSJCqPp3...",
  actKey: "iadem13cpd...",
  rewardReceiveKey: "abc123def456ghi789...",
  h5st: "20250103154523123...",
  eidToken: "tk01m6fb6b2c4xxx..."
}
```

## 🔧 **调试功能特性**

### **✅ 安全性**

1. **敏感信息保护** - 所有敏感信息都只显示前几位+省略号
2. **Cookie截断** - Cookie只显示前100个字符
3. **Token截断** - 各种Token只显示前10-20个字符
4. **完整性保护** - 不影响实际功能，只是增加调试输出

### **✅ 全面性**

1. **Cookie流程** - 从设置到使用的完整跟踪
2. **参数验证** - 所有关键参数的存在性检查
3. **请求跟踪** - 每个API请求的详细参数
4. **错误诊断** - h5st获取失败的详细信息

### **✅ 实用性**

1. **问题定位** - 快速定位Cookie、参数、签名问题
2. **状态监控** - 实时监控各个组件的状态
3. **错误排查** - 详细的错误信息和异常处理
4. **性能监控** - 请求参数和响应状态的监控

## 🚀 **使用方式**

### **正常运行（带调试信息）**

```bash
# 设置Cookie并运行，会自动输出调试信息
export JD_COOKIE="pt_key=xxx;pt_pin=xxx;"
node jd_lottery.js
```

### **调试信息解读**

#### **1. Cookie问题排查**

```
🔍 [调试] 设置Cookie: 无Cookie
🔍 [调试] 提取到用户名: 未提取到
```
**解决方案**: 检查JD_COOKIE环境变量或jdCookie.js文件

#### **2. 参数缺失排查**

```
🔍 [调试] 请求参数: {
  actToken: "空",
  actKey: "空",
  h5st: "空",
  eidToken: "空"
}
```
**解决方案**: 检查活动参数获取和h5st服务

#### **3. h5st签名问题排查**

```
🔍 [调试] h5st请求异常: connect ECONNREFUSED 10.0.0.189:3001
```
**解决方案**: 检查h5st签名服务是否启动

#### **4. jsToken问题排查**

```
🔍 [调试] jsToken初始化结果: 未获取到jsToken
```
**解决方案**: 检查jsToken.js模块和相关依赖

## 🎯 **调试优势**

### **✅ 问题快速定位**

1. **Cookie问题** - 立即发现Cookie设置和格式问题
2. **参数问题** - 快速识别缺失的actToken、actKey等
3. **签名问题** - 详细的h5st获取过程和错误信息
4. **网络问题** - 清晰的请求参数和响应状态

### **✅ 开发友好**

1. **详细日志** - 完整的执行流程跟踪
2. **错误上下文** - 错误发生时的完整环境信息
3. **参数验证** - 自动检查关键参数的有效性
4. **状态监控** - 实时监控各个组件的工作状态

### **✅ 生产可用**

1. **性能影响小** - 调试信息输出不影响主要功能
2. **安全可靠** - 敏感信息得到适当保护
3. **易于理解** - 清晰的调试信息格式
4. **问题追踪** - 便于问题复现和解决

## 🎉 **调试功能完成总结**

### **✅ 增加的调试点**

1. **Cookie设置过程** - 完整的Cookie处理流程
2. **参数提取过程** - actToken、actKey、rewardReceiveKey提取
3. **jsToken初始化** - jsToken获取和设置过程
4. **h5st签名过程** - 签名请求和响应的详细信息
5. **API请求过程** - 每个API请求的完整参数

### **✅ 调试信息特性**

1. **安全性** - 敏感信息适当截断保护
2. **完整性** - 覆盖所有关键执行路径
3. **实用性** - 便于快速定位和解决问题
4. **可读性** - 清晰的格式和标识符

现在的脚本具备了完善的调试功能，能够帮助用户快速定位和解决Cookie、参数、签名等各种问题！
