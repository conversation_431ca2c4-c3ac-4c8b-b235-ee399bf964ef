#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东外卖抽奖活动脚本 - 简化版本
基于抓包分析的纯request实现
"""

import requests
import json
import time

class JDLottery:
    def __init__(self):
        self.session = requests.Session()

        # 固定请求头
        self.session.headers.update({
            'Host': 'api.m.jd.com',
            'Accept': '*/*',
            'x-rp-client': 'h5_1.0.0',
            'Accept-Language': 'zh-cn',
            'Accept-Encoding': 'gzip, deflate, br',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://pro.m.jd.com',
            'User-Agent': 'jdapp;iPhone;15.1.14;;;M/5.0;appBuild/169836;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1748864332%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1;',
            'Referer': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
            'x-referer-page': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
            'Connection': 'keep-alive'
        })

        # 动态参数 - 全部从响应中获取
        self.act_token = ""
        self.act_key = ""
        self.reward_receive_key = ""

    def set_cookies(self, cookies):
        """设置Cookie"""
        cookie_dict = {}
        for cookie in cookies.split(';'):
            cookie = cookie.strip()
            if '=' in cookie:
                key, value = cookie.split('=', 1)
                cookie_dict[key.strip()] = value.strip()
        self.session.cookies.update(cookie_dict)



    def load_activity_data(self):
        """第一步：加载活动数据，从响应中获取所有必要参数"""
        print("🔄 正在加载活动数据...")

        if not self.act_token:
            print("❌ 缺少 act_token，无法继续")
            return False

        if not self.act_key:
            print("❌ 缺少 act_key，无法继续")
            return False

        body_data = {
            "token": self.act_token,
            "commParams": {
                "ubbLoc": "ttf.lqzx",
                "lid": "19_1601_50258_62859",
                "client": 0,
                "sdkToken": "jdd01BWS7QQMQLJE3EDTDUO4CW2RMKFTVHBIBDCDZCV7MYXYYSJ6YIRCACJRP3YL6QNJP3YNB2OQGMP7IOND55OYF67XM2O2YTDAHUDKVNEQ01234567"
            },
            "bizParams": {
                "openChannel": "jdAppHome",
                "actKey": self.act_key,
                "subLabel": ""
            }
        }

        data = {
            'appid': 'day_day_reward',
            'functionId': 'comp_data_load',
            'loginType': '2',
            'loginWQBiz': 'tttwxapp',
            'body': json.dumps(body_data, separators=(',', ':')),
            'h5st': '',  # 需要实现h5st算法
            'x-api-eid-token': ''
        }

        try:
            response = self.session.post('https://api.m.jd.com/client.action', data=data)
            result = response.json()

            if result.get('success'):
                print("✅ 活动数据加载成功")

                # 提取rewardReceiveKey
                data = result.get('data', {})
                reward_progress_items = data.get('rewardProgressItems', [])
                if reward_progress_items:
                    for item in reward_progress_items:
                        reward_key = item.get('rewardReceiveKey', '')
                        if reward_key:
                            self.reward_receive_key = reward_key
                            print(f"📝 获取到 rewardReceiveKey: {reward_key[:30]}...")
                            break

                return True
            else:
                print(f"❌ 活动数据加载失败: {result.get('message', '未知错误')}")
                return False

        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False

    def claim_reward(self):
        """第二步：领取奖励"""
        print("🎁 正在领取奖励...")

        if not self.reward_receive_key:
            print("❌ 缺少 rewardReceiveKey，请先加载活动数据")
            return False

        body_data = {
            "token": self.act_token,
            "fnCode": "invoke",
            "commParams": {
                "longitude": "113.328752",
                "latitude": "23.17921",
                "ubbLoc": "ttf.lqzx",
                "lid": "19_1601_50258_62859",
                "client": 0,
                "sdkToken": "jdd01BWS7QQMQLJE3EDTDUO4CW2RMKFTVHBIBDCDZCV7MYXYYSJ6YIRCACJRP3YL6QNJP3YNB2OQGMP7IOND55OYF67XM2O2YTDAHUDKVNEQ01234567"
            },
            "bizParams": {
                "rewardReceiveKey": self.reward_receive_key,
                "openChannel": "jdAppHome",
                "actFlowCode": "receiveReward",
                "actKey": self.act_key,
                "subLabel": ""
            }
        }

        data = {
            'appid': 'day_day_reward',
            'functionId': 'comp_data_interact',
            'loginType': '2',
            'loginWQBiz': 'tttwxapp',
            'body': json.dumps(body_data, separators=(',', ':')),
            'h5st': '',  # 需要实现h5st算法
            'x-api-eid-token': ''
        }

        try:
            response = self.session.post('https://api.m.jd.com/client.action', data=data)
            result = response.json()

            if result.get('success'):
                print("🎉 奖励领取成功!")

                # 解析奖励信息
                data = result.get('data', {})
                reward_info_list = data.get('rewardInfoList', [])
                if reward_info_list:
                    for reward in reward_info_list:
                        coupon_info = reward.get('couponInfo', {})
                        if coupon_info:
                            discount = coupon_info.get('couponDiscount', 0)
                            quota = coupon_info.get('couponQuota', 0)
                            limit_str = coupon_info.get('couponLimitStr', '')
                            print(f"🎫 获得优惠券: {limit_str} {discount}元券 (满{quota}元可用)")

                return True
            else:
                print(f"❌ 奖励领取失败: {result.get('message', '未知错误')}")
                return False

        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False

    def run(self):
        """执行完整抽奖流程"""
        print("🚀 开始执行抽奖...")

        # 第一步：加载活动数据
        if not self.load_activity_data():
            return False

        # 等待一下
        time.sleep(1)

        # 第二步：领取奖励
        if not self.claim_reward():
            return False

        print("✅ 抽奖完成!")
        return True


def main():
    """主函数"""
    print("=" * 50)
    print("京东外卖抽奖活动脚本 - 简化版本")
    print("=" * 50)

    # 创建实例
    lottery = JDLottery()

    # 设置Cookie (需要用户提供)
    cookies = """
    sdtoken=AAbEsBpEIOVjqTAKCQtvQu17uCPhmHjpU5ZKh7k7xAhmuEVsq-JkZ3luf9QG1sOt9CtLppgrhk7vPWXToHJDTbWrkHyYE2m6k47jzPU2aQB4b4YBPgK0-CaghBA;
    pt_key=app_openAAJoPXKMADDCQtjfDN1ei7zngTtGDgG6HrDf_Ei_YIp2N7tg8fIrrbpS6Xl1TEFJsD_jiAXkIW4;
    pt_pin=jd_4b25e12eb2177;
    3AB9D23F7A4B3CSS=jdd03IFRDI7JL55ZJCFWHCCFV35GLGY4BZH4LAVWYCMXUV2W6AU3YZZ5TWUYNRD565WJQS4XHZWCHFZUN5WOPAAU7UVYUZMAAAAMXGBYA7QAAAAAADLU6IBRAYBHW44X
    """.strip()

    lottery.set_cookies(cookies)

    # 设置必要参数 (需要从响应中获取)
    lottery.act_token = "pVQSJCqPp3oXPfH9Y28Tv"  # 需要替换为真实值
    lottery.act_key = "iadem13cpdiof3vtyykmr"     # 需要替换为真实值

    # 执行抽奖
    success = lottery.run()

    if success:
        print("\n🎉 抽奖成功完成!")
    else:
        print("\n❌ 抽奖失败")


if __name__ == "__main__":
    main()
