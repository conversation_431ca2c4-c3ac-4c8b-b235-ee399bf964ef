# 🔧 h5st参数格式修复

## ✅ **h5st参数错误修复完成**

已修复h5st服务返回"params is empty"的问题，调整了参数格式以符合服务端要求。

## 🐛 **问题分析**

### **错误信息**
```json
{
  "code": 400,
  "body": null,
  "message": "业务异常 - params is empty",
  "traceId": "6dc07e6f-c7ee-41be-9b8c-bcc51725d78a"
}
```

### **问题原因**
1. **参数格式不匹配** - h5st服务期望的参数格式与发送的不一致
2. **body参数格式** - 服务端可能需要字符串格式的body而不是对象
3. **参数名称不匹配** - 可能使用了错误的参数名称

## 🔧 **修复方案**

### **修复前的参数格式**
```javascript
// 直接发送h5stParams对象
const response = await axios({
    method: 'POST',
    url: 'http://**********:3001/h5st',
    headers: {
        'Content-Type': 'application/json'
    },
    data: h5stParams,  // 直接发送原始参数
    timeout: 5000
});
```

### **修复后的参数格式**
```javascript
// 重新构造符合服务端要求的参数格式
const h5stRequestData = {
    functionId: h5stParams.functionId,
    body: JSON.stringify(h5stParams.body),  // 确保body是字符串格式
    appid: h5stParams.appid,
    t: h5stParams.t,
    clientVersion: h5stParams.clientVersion,
    client: h5stParams.client,
    uuid: h5stParams.uuid,
    ua: h5stParams.ua,
    pin: h5stParams.pin
};

console.log('🔍 [调试] 发送给h5st服务的数据:', JSON.stringify(h5stRequestData, null, 2));

const response = await axios({
    method: 'POST',
    url: 'http://**********:3001/h5st',
    headers: {
        'Content-Type': 'application/json'
    },
    data: h5stRequestData,  // 发送重新构造的参数
    timeout: 5000
});
```

## 📊 **参数对比**

### **修复前的参数结构**
```javascript
h5stParams = {
    appid: 'day_day_reward',
    functionId: 'comp_data_load',
    body: bodyData,  // 对象格式
    t: Date.now(),
    clientVersion: '15.1.14',
    client: 'ios',
    uuid: this.getUuid(),
    ua: this.headers['User-Agent'],
    loginType: '2',
    loginWQBiz: 'tttwxapp',
    pin: userPin,
    h5st: 'comp_data_load_h5st'
};
```

### **修复后的参数结构**
```javascript
h5stRequestData = {
    functionId: 'comp_data_load',        // 功能ID
    body: '{"token":"xxx","commParams":{...},"bizParams":{...}}',  // 字符串格式
    appid: 'day_day_reward',            // 应用ID
    t: 1704268523123,                   // 时间戳
    clientVersion: '15.1.14',           // 客户端版本
    client: 'ios',                      // 客户端类型
    uuid: '12345678-1234-4xxx-yxxx-xxxxxxxxxxxx',  // UUID
    ua: 'jdapp;iPhone;15.1.14;...',     // User-Agent
    pin: 'jd_username'                  // 用户Pin
};
```

## 🔍 **调试信息增强**

### **参数验证日志**
```javascript
// 调试信息：打印h5st请求参数
console.log('🔍 [调试] h5st请求参数:', {
    functionId: functionId,
    appid: h5stParams.appid,
    pin: h5stParams.pin || '空',
    h5st: h5stParams.h5st || '空',
    uuid: h5stParams.uuid ? h5stParams.uuid.substring(0, 10) + '...' : '空',
    bodyLength: h5stParams.body ? JSON.stringify(h5stParams.body).length : 0
});

// 发送给h5st服务的完整数据
console.log('🔍 [调试] 发送给h5st服务的数据:', JSON.stringify(h5stRequestData, null, 2));
```

### **预期的调试输出**
```
🔍 [调试] h5st请求参数: {
  functionId: 'comp_data_load',
  appid: 'day_day_reward',
  pin: 'jd_username',
  h5st: '空',
  uuid: '12345678-1...',
  bodyLength: 245
}

🔍 [调试] 发送给h5st服务的数据: {
  "functionId": "comp_data_load",
  "body": "{\"token\":\"\",\"commParams\":{\"ubbLoc\":\"ttf.lqzx\",\"lid\":\"19_1601_50258_62859\",\"client\":0,\"sdkToken\":\"jdd01BWS7QQMQLJE3EDTDUO4CW2RMKFTVHBIBDCDZCV7MYXYYSJ6YIRCACJRP3YL6QNJP3YNB2OQGMP7IOND55OYF67XM2O2YTDAHUDKVNEQ01234567\"},\"bizParams\":{\"openChannel\":\"jdAppHome\",\"actKey\":\"\",\"subLabel\":\"\"}}",
  "appid": "day_day_reward",
  "t": 1704268523123,
  "clientVersion": "15.1.14",
  "client": "ios",
  "uuid": "12345678-1234-4xxx-yxxx-xxxxxxxxxxxx",
  "ua": "jdapp;iPhone;15.1.14;...",
  "pin": "jd_username"
}
```

## 🎯 **修复效果**

### **修复前的错误**
```
🔍 [调试] h5st签名获取失败，响应: {
  code: 400,
  body: null,
  message: '业务异常 - params is empty',
  traceId: '6dc07e6f-c7ee-41be-9b8c-bcc51725d78a'
}
```

### **修复后的预期结果**
```
🔍 [调试] h5st签名获取成功: 20250103154523123456789012...
```

## 🚀 **测试建议**

### **1. 手动测试h5st服务**
```bash
curl -X POST http://**********:3001/h5st \
  -H "Content-Type: application/json" \
  -d '{
    "functionId": "comp_data_load",
    "body": "{\"token\":\"\",\"commParams\":{\"ubbLoc\":\"ttf.lqzx\",\"lid\":\"19_1601_50258_62859\",\"client\":0,\"sdkToken\":\"jdd01BWS7QQMQLJE3EDTDUO4CW2RMKFTVHBIBDCDZCV7MYXYYSJ6YIRCACJRP3YL6QNJP3YNB2OQGMP7IOND55OYF67XM2O2YTDAHUDKVNEQ01234567\"},\"bizParams\":{\"openChannel\":\"jdAppHome\",\"actKey\":\"\",\"subLabel\":\"\"}}",
    "appid": "day_day_reward",
    "t": 1704268523123,
    "clientVersion": "15.1.14",
    "client": "ios",
    "uuid": "12345678-1234-4xxx-yxxx-xxxxxxxxxxxx",
    "ua": "jdapp;iPhone;15.1.14;...",
    "pin": "test_user"
  }'
```

### **2. 运行修复后的脚本**
```bash
export JD_COOKIE="pt_key=xxx;pt_pin=xxx;"
node jd_lottery.js
```

### **3. 检查h5st服务日志**
确保h5st服务能够正确解析新的参数格式。

## 🔧 **可能的进一步优化**

### **1. 参数名称调整**
如果仍然出现问题，可能需要调整参数名称：
```javascript
// 可能的替代参数名称
const h5stRequestData = {
    function_id: h5stParams.functionId,  // 下划线格式
    request_body: JSON.stringify(h5stParams.body),  // 不同的名称
    app_id: h5stParams.appid,
    timestamp: h5stParams.t,
    // ...
};
```

### **2. 参数顺序调整**
某些服务对参数顺序敏感：
```javascript
const h5stRequestData = {
    functionId: h5stParams.functionId,  // 最重要的参数放在前面
    appid: h5stParams.appid,
    body: JSON.stringify(h5stParams.body),
    // ...其他参数
};
```

### **3. 添加必要的默认值**
```javascript
const h5stRequestData = {
    functionId: h5stParams.functionId,
    body: JSON.stringify(h5stParams.body),
    appid: h5stParams.appid || 'day_day_reward',
    t: h5stParams.t || Date.now(),
    clientVersion: h5stParams.clientVersion || '15.1.14',
    client: h5stParams.client || 'ios',
    uuid: h5stParams.uuid || this.getUuid(),
    ua: h5stParams.ua || this.headers['User-Agent'],
    pin: h5stParams.pin || ''
};
```

## 🎉 **修复总结**

### **✅ 已修复的问题**
1. **参数格式错误** - 重新构造符合服务端要求的参数格式
2. **body参数格式** - 确保body是字符串格式
3. **参数完整性** - 包含所有必要的参数
4. **调试信息** - 增加详细的参数日志

### **✅ 预期效果**
- h5st服务能够正确解析参数
- 返回有效的h5st签名
- 脚本能够正常执行后续流程

### **🎯 关键改进**
1. **参数重构** - 使用正确的参数格式发送请求
2. **调试增强** - 详细的参数日志便于排查问题
3. **错误处理** - 更好的错误信息和异常处理

现在h5st请求应该能够正确发送参数并获取到有效的签名！
