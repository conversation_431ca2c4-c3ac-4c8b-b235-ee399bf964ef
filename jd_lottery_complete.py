#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东外卖抽奖活动自动化脚本
基于抓包分析的完整请求实现

主要差异分析:
1. request(2).txt - 领取奖励接口 (comp_data_interact)
2. request(3).txt - 加载活动数据接口 (comp_data_load)

关键差异:
- functionId不同: comp_data_interact vs comp_data_load
- body参数结构不同: interact包含fnCode和rewardReceiveKey
- 调用顺序: 必须先load再interact
"""

import requests
import json
import time
import hashlib
import random
import urllib.parse
from typing import Dict, Any, Optional

class JDLotteryBot:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://api.m.jd.com/client.action"
        self.headers = {
            'Host': 'api.m.jd.com',
            'Accept': '*/*',
            'x-rp-client': 'h5_1.0.0',
            'Accept-Language': 'zh-cn',
            'Accept-Encoding': 'gzip, deflate, br',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://pro.m.jd.com',
            'User-Agent': 'jdapp;iPhone;15.1.14;;;M/5.0;appBuild/169836;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1748864332%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1;',
            'Referer': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
            'x-referer-page': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
            'Connection': 'keep-alive'
        }

        # 需要用户提供的认证信息
        self.cookies = {}
        self.token = ""
        self.act_key = "iadem13cpdiof3vtyykmr"
        self.reward_receive_key = ""

    def set_auth_info(self, cookies: str, token: str = ""):
        """
        设置认证信息
        :param cookies: Cookie字符串，从抓包中获取
        :param token: 从抓包中获取的token
        """
        # 解析Cookie字符串
        for cookie in cookies.split(';'):
            if '=' in cookie:
                key, value = cookie.strip().split('=', 1)
                self.cookies[key] = value

        self.session.cookies.update(self.cookies)
        if token:
            self.token = token

    def generate_h5st(self, function_id: str, body: str) -> str:
        """
        生成h5st参数 (简化版本，实际需要逆向算法)
        这里提供一个基础框架，实际使用需要完整的算法实现

        从抓包分析可知h5st格式为:
        timestamp;algorithm_result;app_id;signature
        """
        timestamp = str(int(time.time() * 1000))
        # 这里需要实现完整的h5st算法
        # 当前返回示例格式，实际使用需要替换为真实算法
        app_id = "93453" if function_id == "comp_data_interact" else "ec373"
        return f"{timestamp};sample_h5st_value;{app_id};tk03sample"

    def load_activity_data(self) -> Optional[Dict[str, Any]]:
        """
        加载活动数据 (对应request(3).txt)
        这是第一步，必须先调用此接口获取活动状态和rewardReceiveKey
        """
        print("🔄 正在加载活动数据...")

        body_data = {
            "token": self.token,
            "commParams": {
                "ubbLoc": "ttf.lqzx",
                "lid": "19_1601_50258_62859",
                "client": 0,
                "sdkToken": "jdd01BWS7QQMQLJE3EDTDUO4CW2RMKFTVHBIBDCDZCV7MYXYYSJ6YIRCACJRP3YL6QNJP3YNB2OQGMP7IOND55OYF67XM2O2YTDAHUDKVNEQ01234567"
            },
            "bizParams": {
                "openChannel": "jdAppHome",
                "actKey": self.act_key,
                "subLabel": ""
            }
        }

        body_json = json.dumps(body_data, separators=(',', ':'))
        h5st = self.generate_h5st("comp_data_load", body_json)

        data = {
            'appid': 'day_day_reward',
            'functionId': 'comp_data_load',
            'loginType': '2',
            'loginWQBiz': 'tttwxapp',
            'body': body_json,
            'h5st': h5st,
            'x-api-eid-token': self.cookies.get('3AB9D23F7A4B3CSS', '')
        }

        try:
            response = self.session.post(self.base_url, data=data, headers=self.headers)
            response.raise_for_status()
            result = response.json()

            if result.get('success'):
                print("✅ 活动数据加载成功")
                data = result.get('data', {})

                # 提取rewardReceiveKey用于后续领取操作
                reward_progress_items = data.get('rewardProgressItems', [])
                if reward_progress_items:
                    self.reward_receive_key = reward_progress_items[0].get('rewardReceiveKey', '')
                    print(f"📝 获取到rewardReceiveKey: {self.reward_receive_key[:20]}...")

                return data
            else:
                print(f"❌ 活动数据加载失败: {result.get('message', '未知错误')}")
                return None

        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
            return None

    def claim_reward(self) -> Optional[Dict[str, Any]]:
        """
        领取奖励 (对应request(2).txt)
        这是第二步，使用从load_activity_data获取的rewardReceiveKey
        """
        if not self.reward_receive_key:
            print("❌ 缺少rewardReceiveKey，请先调用load_activity_data")
            return None

        print("🎁 正在领取奖励...")

        body_data = {
            "token": self.token,
            "fnCode": "invoke",
            "commParams": {
                "longitude": "113.328752",
                "latitude": "23.17921",
                "ubbLoc": "ttf.lqzx",
                "lid": "19_1601_50258_62859",
                "client": 0,
                "sdkToken": "jdd01BWS7QQMQLJE3EDTDUO4CW2RMKFTVHBIBDCDZCV7MYXYYSJ6YIRCACJRP3YL6QNJP3YNB2OQGMP7IOND55OYF67XM2O2YTDAHUDKVNEQ01234567"
            },
            "bizParams": {
                "rewardReceiveKey": self.reward_receive_key,
                "openChannel": "jdAppHome",
                "actFlowCode": "receiveReward",
                "actKey": self.act_key,
                "subLabel": ""
            }
        }

        body_json = json.dumps(body_data, separators=(',', ':'))
        h5st = self.generate_h5st("comp_data_interact", body_json)

        data = {
            'appid': 'day_day_reward',
            'functionId': 'comp_data_interact',
            'loginType': '2',
            'loginWQBiz': 'tttwxapp',
            'body': body_json,
            'h5st': h5st,
            'x-api-eid-token': self.cookies.get('3AB9D23F7A4B3CSS', '')
        }

        try:
            response = self.session.post(self.base_url, data=data, headers=self.headers)
            response.raise_for_status()
            result = response.json()

            if result.get('success'):
                print("🎉 奖励领取成功!")
                data = result.get('data', {})

                # 解析奖励信息
                reward_info_list = data.get('rewardInfoList', [])
                if reward_info_list:
                    for reward in reward_info_list:
                        coupon_info = reward.get('couponInfo', {})
                        if coupon_info:
                            discount = coupon_info.get('couponDiscount', 0)
                            quota = coupon_info.get('couponQuota', 0)
                            limit_str = coupon_info.get('couponLimitStr', '')
                            print(f"🎫 获得优惠券: {limit_str} {discount}元券 (满{quota}元可用)")

                return data
            else:
                print(f"❌ 奖励领取失败: {result.get('message', '未知错误')}")
                return None

        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
            return None

    def run_lottery(self) -> bool:
        """
        执行完整的抽奖流程
        """
        print("🚀 开始执行抽奖流程...")

        # 第一步：加载活动数据
        activity_data = self.load_activity_data()
        if not activity_data:
            return False

        # 等待一下，模拟真实用户行为
        time.sleep(1)

        # 第二步：领取奖励
        reward_data = self.claim_reward()
        if not reward_data:
            return False

        print("✅ 抽奖流程执行完成!")
        return True


def main():
    """
    使用示例
    """
    print("=" * 50)
    print("京东外卖抽奖活动自动化脚本")
    print("=" * 50)

    # 创建机器人实例
    bot = JDLotteryBot()

    # 设置认证信息 (需要从抓包中获取)
    cookies = """
    sdtoken=AAbEsBpEIOVjqTAKCQtvQu17uCPhmHjpU5ZKh7k7xAhmuEVsq-JkZ3luf9QG1sOt9CtLppgrhk7vPWXToHJDTbWrkHyYE2m6k47jzPU2aQB4b4YBPgK0-CaghBA;
    pt_key=app_openAAJoPXKMADDCQtjfDN1ei7zngTtGDgG6HrDf_Ei_YIp2N7tg8fIrrbpS6Xl1TEFJsD_jiAXkIW4;
    pt_pin=jd_4b25e12eb2177;
    3AB9D23F7A4B3CSS=jdd03IFRDI7JL55ZJCFWHCCFV35GLGY4BZH4LAVWYCMXUV2W6AU3YZZ5TWUYNRD565WJQS4XHZWCHFZUN5WOPAAU7UVYUZMAAAAMXGBYA7QAAAAAADLU6IBRAYBHW44X
    """.strip()

    token = "pVQSJCqPp3oXPfH9Y28Tv"

    # 设置认证信息
    bot.set_auth_info(cookies, token)

    # 执行抽奖
    success = bot.run_lottery()

    if success:
        print("\n🎉 抽奖成功完成!")
    else:
        print("\n❌ 抽奖失败，请检查配置和网络")


if __name__ == "__main__":
    main()


"""
使用说明:

1. 安装依赖:
   pip install requests

2. 获取认证信息:
   - 使用抓包工具(如ProxyPin)抓取京东APP的请求
   - 从请求中提取Cookie和token
   - 替换main函数中的cookies和token

3. 重要参数说明:
   - cookies: 包含用户认证信息，从抓包中获取
   - token: 活动token，从抓包的body中获取
   - h5st: 防刷参数，需要逆向算法生成(当前为示例)

4. 接口差异总结:

   request(2).txt (comp_data_interact) - 领取奖励:
   - 包含fnCode: "invoke"
   - 包含rewardReceiveKey (从load接口获取)
   - 包含actFlowCode: "receiveReward"
   - 返回具体奖励信息

   request(3).txt (comp_data_load) - 加载活动:
   - 不包含fnCode
   - 不包含rewardReceiveKey
   - 返回活动配置和进度信息
   - 提供rewardReceiveKey供后续使用

5. 调用顺序:
   load_activity_data() -> claim_reward()
   必须先加载活动数据获取rewardReceiveKey，再执行领取操作

6. 注意事项:
   - h5st参数需要实现完整的算法，当前为示例
   - Cookie和token有时效性，需要定期更新
   - 请遵守相关服务条款，合理使用
"""