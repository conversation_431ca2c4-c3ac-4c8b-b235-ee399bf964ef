#!/usr/bin/env node
/**
 * 京东外卖抽奖活动脚本 - JavaScript版本
 * 基于抓包分析的完整实现，集成jsToken.js
 */

const axios = require('axios');
const qs = require('qs');
const { getJsToken } = require('./utils/jsToken.js');
const { setBaseCookie } = require('./utils/baseCookie.js');
const { BaseUtils } = require('./utils/baseUtils.js');

const comp_data_load_h5st = "20250602193855954%3Bgzixaww9w3w3amw4%3Bec373%3Btk05w204358a341lMyszWTliNTJWsmuh35YW7S3i3J7R3poQFF3VuueuXs7Ty-4h4eIaBeqg8GadiZbV9WIWIhYWKtrV%3B74fc73bf10b7da690dd855061580f106a32caaeb8731f4f77957d799cc9119f1%3B5.1%3B1748864334954%3BsmePkmsh35YW7S3i3J7R3poQFF3VMuMgMuHVMusmk_Mm1qIhIlbhJZIhIhLh2m7hJZYV5WLh4eIW_m7W7aIWLtLmOGLm_VqTHlYV3lsmOGujMabW7ioiJpoiLtbiJJ7i7WLW6aoi4iIiLtLiIt7iLVYhMuMgMiXW41YWLlsmOGuj96sm0msh5lImOuMsCmshAqLj5W3XJ9YUIxZhGlsm0mMRMusmk_MmbJZiEh6iexKilZqcnlsm0mcT-dITNlHmOuMsCmcVAxoUeJImOGLmItHmOuMsC6nmOGOiOGLm9qbRMlsmOusmk_ci9uMgMubi5lImOusmOGuj26sm0mMi9aHWMusmOuMsCm8dWpYd96qiWlrdU9IYMuMgM64TK1YW8lsmOusmk_siOGLm2aHWMusmOuMsCurm0m8h5lImOusmOGuj9irm0mMh5lImOusmOGuj_uMgMabRMlsmOusmk_siOGLm6aHWMusmOuMsCm7hOGLm7aHWMusmOuMsCmchAqLj_yZV6JoTMuMgMqbRMlsmOusmk_siOGLmDRHmOusmOGuj96sm0m8SClsmOusmk_siOGLmClsmOusmk_siOGLmKRHmOusmOG_QOGLmK1YV6NXVMusmk_cPOuMsMS7h9mLWJlbiJZLiMd7XKFImOGLm9uHmOusmOG_QOGLm_tHmOuMsCmsYOi5bOiYWhtcVDJoTOq7X6qrmbxqmJ14TGtZUOapart8gJ14TGtZUMuMgMqYR7lsmOG_Q%3Bd1d935e7f82d3986883b8f1b8b21e4029a9f48dd9ae09b8d0d5b691d4197e609%3Bri_uKJKT-JoRL1YRI9cQKxIWCeYU_tXW"
const comp_data_interact_h5st ="20250602193856941%3Baixipa3axq30h2w9%3B93453%3Btk03w8a3b1bc918natIf0E29uONqJ5ocYU1pPM6VE0PRy_jt2dvpB-1e3_5ZIQWEQwtcAwwONZR1hzSPVH7RCavwqg0V%3B6577523268131c9924328bc1545d0a1eee06ae6a48f982160b431d3677e033cb%3B5.1%3B1748864335941%3BsmePkmcg3lrU_ibS2p4iNtXU2JYWMuMgMuHVMusmk_Mm1qIhIlbhJZIhIhLh2m7hJZYV5WLh4eIW_m7W7aIWLtLmOGLm_VqTHlYV3lsmOGujMabW7ioiJpoiLtbiJJ7i7WLW6aoi4iIiLtLiIt7iLVYhMuMgMiXW41YWLlsmOGuj96sm0msh5lImOuMsCmshAqLj5W3XJ9YUIxZhGlsm0mMRMusmk_Mm9G6Tn54i_OLinBai3msm0mcT-dITNlHmOuMsCmcVAxoUeJImOGLmItHmOuMsC6nmOGOiOGLm9qbRMlsmOusmk_ci9uMgMubi5lImOusmOGuj26sm0mMi9aHWMusmOuMsCmcWmxZbHVJTElbYmJZUMuMgM64TK1YW8lsmOusmk_siOGLm2aHWMusmOuMsCurm0m8h5lImOusmOGuj9irm0mMh5lImOusmOGuj_uMgMabRMlsmOusmk_siOGLm6aHWMusmOuMsCm7hOGLm7aHWMusmOuMsCmchAqLj_yZV6JoTMuMgMqbRMlsmOusmk_siOGLmDRHmOusmOGuj96sm0m8SClsmOusmk_siOGLmClsmOusmk_siOGLmKRHmOusmOG_QOGLmK1YV6NXVMusmk_cPOuMsMS7h9mLWJlbiJZLiMd7XKFImOGLm9uHmOusmOG_QOGLm_tHmOuMsCmsYOi5bOiYWhtcVDJoTOq7X6qrmbxqmJ14TGtZUOapart8gJ14TGtZUMuMgMqYR7lsmOG_Q%3Bc42983c57092a7ec97d8052d4c8d06ee092b9763eb1c20d323e828fe90cd17f7%3Bri_uKJKT-JoRL1YRI9cQKxIWCeYU_tXW"
// 引入Cookie (支持环境变量和文件两种方式)
const jdCookieNode = process.env.JD_COOKIE ? process.env.JD_COOKIE.split('&') : require('./jdCookie.js');
let cookiesArr = [];
if (Array.isArray(jdCookieNode)) {
    cookiesArr = jdCookieNode;
} else if (typeof jdCookieNode === 'object') {
    Object.keys(jdCookieNode).forEach((item) => {
        cookiesArr.push(jdCookieNode[item]);
    });
} else {
    console.error('无法获取 cookies');
    process.exit(1);
}

// 加载依赖模块 (参照jdtry静默处理)
let SmashUtils = null;
try {
    const smashModule = require('./utils/smashUtils.js');
    SmashUtils = smashModule.SmashUtils;
} catch (error) {
    // 静默处理加载失败
}

class JDLottery {
    constructor() {
        // 初始化全局变量 (完全参照jdtry)
        this.initJdtryGlobals();

        // 固定请求头
        this.headers = {
            'Host': 'api.m.jd.com',
            'Accept': '*/*',
            'x-rp-client': 'h5_1.0.0',
            'Accept-Language': 'zh-cn',
            'Accept-Encoding': 'gzip, deflate, br',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://pro.m.jd.com',
            'User-Agent': 'jdapp;iPhone;15.1.14;;;M/5.0;appBuild/169836;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1748864332%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1;',
            'Referer': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
            'x-referer-page': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
            'Connection': 'keep-alive'
        };

        // 动态参数
        this.actToken = '';
        this.actKey = '';
        this.rewardReceiveKey = '';
        this.cookies = '';
        this.jsToken = '';
        this.uuid = '';
        this.smashUtils = null;
    }

    /**
     * 初始化jdtry标准的全局变量
     */
    initJdtryGlobals() {
        // 初始化全局$对象 (完全参照jdtry)
        global.$ = global.$ || {
            jsToken: '',
            UserName: '',
            cache: {},
            smashUtils: null,
            nickName: '',
            index: 0
        };

        // 初始化baseUtils (如果不存在)
        if (!global.baseUtils) {
            try {
                global.baseUtils = new BaseUtils();
            } catch (error) {
                // 静默处理
            }
        }
    }

    /**
     * 设置Cookie (完全参照jdtry方式)
     * @param {string} cookies - 用户Cookie字符串 (pt_key=xx;pt_pin=xx;格式)
     */
    async setCookies(cookies) {
        // 调试信息：打印Cookie设置过程
        console.log('🔍 [调试] 设置Cookie:', cookies ? cookies.substring(0, 100) + '...' : '无Cookie');

        // 设置用户Cookie到实例和全局$
        this.cookies = cookies;
        global.$.UserName = this.extractPtPin(cookies);

        console.log('🔍 [调试] 提取到用户名:', global.$.UserName || '未提取到');

        // 1. 初始化SmashUtils环境 (参照jdtry的initSmashUtils)
        await this.initSmashUtils(cookies);

        // 2. 初始化jsToken (参照jdtry的initJsToken)
        await this.initJsToken();

        console.log('🔍 [调试] jsToken初始化结果:', this.jsToken ? this.jsToken.substring(0, 20) + '...' : '未获取到jsToken');

        // 3. 设置请求头Cookie (jdtry方式：直接使用用户Cookie)
        this.headers['Cookie'] = cookies;
        console.log('🔍 [调试] Cookie已设置到请求头');
    }

    /**
     * 提取pt_pin (参照jdtry方式)
     */
    extractPtPin(cookies) {
        const regex = /pt_pin=([^;]+)/;
        const match = cookies.match(regex);
        return match && match[1] ? decodeURIComponent(match[1]) : '';
    }

    /**
     * 初始化SmashUtils (完全参照jdtry的initSmashUtils)
     */
    async initSmashUtils(cookies) {
        if (!SmashUtils) return;

        try {
            const url = 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html';
            const userAgent = this.headers['User-Agent'];

            // 创建SmashUtils实例 (参照jdtry)
            this.smashUtils = new SmashUtils(url, cookies, userAgent);
            global.$.smashUtils = this.smashUtils;

            // 执行jdtry标准初始化流程
            this.smashUtils.getLocalData();
            this.smashUtils.getAppOs();
            this.smashUtils.getBlog();
            this.smashUtils.getFpv();

            // 获取设备信息
            await this.smashUtils.getInfo();

            // 设置joyya Cookie (风控)
            this.smashUtils.setjoyyaCookie('init');

            // 获取JR信息
            this.smashUtils.getJrInfo();

            // 初始化SmashUtils (参照jdtry的initial调用)
            await this.smashUtils.initial({
                appId: '50170_',
                debug: false,
                preRequest: true,
                onSign: (data) => data,
                onRequestTokenRemotely: async () => null
            });

        } catch (error) {
            // 静默处理错误
        }
    }

    /**
     * 初始化jsToken (完全参照jdtry的initJsToken)
     */
    async initJsToken() {
        try {
            if (!this.jsToken) {
                const userAgent = this.headers['User-Agent'];
                const url = 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html';
                const bizId = 'day_day_reward';

                this.jsToken = await getJsToken(userAgent, url, bizId);

                // 同步到全局$ (jdtry标准)
                global.$.jsToken = this.jsToken;

                console.log('🔍 [调试] jsToken获取结果:', this.jsToken ? '成功' : '失败');
            }
        } catch (error) {
            console.log('🔍 [调试] jsToken获取异常:', error.message);
            // 静默处理错误
        }
    }



    /**
     * 获取h5st签名
     * @param {string} functionId - 功能ID
     * @param {object} bodyData - 请求体数据
     * @returns {string} h5st签名
     */
    async getH5st(functionId, bodyData) {
        try {
            // 获取用户Pin (从全局$或Cookie中提取)
            const userPin = global.$.UserName || this.extractPtPin(this.cookies) || '';

            let h5stParams;

            if (functionId === 'comp_data_load') {
                // comp_data_load请求的h5st参数
                h5stParams = {
                    appid: 'day_day_reward',
                    functionId: 'comp_data_load',
                    body: bodyData,  // 修复：直接传递对象而不是字符串
                    t: Date.now(),
                    clientVersion: '15.1.14',
                    client: 'ios',
                    uuid: this.getUuid(),
                    ua: this.headers['User-Agent'],
                    loginType: '2',
                    loginWQBiz: 'tttwxapp',
                    pin: userPin,  // 增加账号Pin
                    h5st: comp_data_load_h5st  // 活动对应的h5st标识
                };
            } else if (functionId === 'comp_data_interact') {
                // comp_data_interact请求的h5st参数
                h5stParams = {
                    appid: 'day_day_reward',
                    functionId: 'comp_data_interact',
                    body: bodyData,  // 修复：直接传递对象而不是字符串
                    t: Date.now(),
                    clientVersion: '15.1.14',
                    client: 'ios',
                    uuid: this.getUuid(),
                    ua: this.headers['User-Agent'],
                    loginType: '2',
                    loginWQBiz: 'tttwxapp',
                    pin: userPin,  // 增加账号Pin
                    h5st: 'comp_data_interact_h5st'  // 活动对应的h5st标识
                };
            } else {
                // 默认参数
                h5stParams = {
                    appid: 'day_day_reward',
                    functionId: functionId,
                    body: bodyData,  // 修复：直接传递对象而不是字符串
                    t: Date.now(),
                    clientVersion: '15.1.14',
                    client: 'ios',
                    uuid: this.getUuid(),
                    ua: this.headers['User-Agent'],
                    pin: userPin,  // 增加账号Pin
                    h5st: `${functionId}_h5st`  // 活动对应的h5st标识
                };
            }

            // 调试信息：打印h5st请求参数
            console.log('🔍 [调试] h5st请求参数:', {
                functionId: functionId,
                appid: h5stParams.appid,
                pin: h5stParams.pin || '空',
                h5st: h5stParams.h5st || '空',
                uuid: h5stParams.uuid ? h5stParams.uuid.substring(0, 10) + '...' : '空',
                bodyLength: h5stParams.body ? JSON.stringify(h5stParams.body).length : 0
            });

            // 修复h5st请求格式 - 使用最基本的参数结构
            const h5stRequestData = {
                functionId: h5stParams.functionId,
                body: JSON.stringify(h5stParams.body),
                appid: h5stParams.appid,
                t: h5stParams.t,
                clientVersion: h5stParams.clientVersion,
                client: h5stParams.client,
                uuid: h5stParams.uuid,
                ua: h5stParams.ua,
                pin: h5stParams.pin
            };

            console.log('🔍 [调试] 发送给h5st服务的数据:', JSON.stringify(h5stRequestData, null, 2));

            const response = await axios({
                method: 'POST',
                url: 'http://10.0.0.189:3001/h5st',
                headers: {
                    'Content-Type': 'application/json'
                },
                data: h5stRequestData,
                timeout: 5000
            });

            if (response.data && response.data.h5st) {
                console.log('🔍 [调试] h5st签名获取成功:', response.data.h5st.substring(0, 30) + '...');
                return response.data.h5st;
            }

            console.log('🔍 [调试] h5st签名获取失败，响应:', response.data);
            return '';
        } catch (error) {
            console.log('🔍 [调试] h5st请求异常:', error.message);
            return '';
        }
    }

    /**
     * 获取UUID
     */
    getUuid() {
        if (!this.uuid) {
            this.uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c === 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }
        return this.uuid;
    }

    /**
     * 获取活动基础信息 (从活动页面或初始API获取actToken和actKey)
     */
    async getActivityInfo() {
        try {
            console.log('🔍 正在获取活动基础信息...');

            // 方法1: 尝试访问活动页面获取基础参数
            const activityUrl = 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html';

            const response = await axios({
                method: 'GET',
                url: activityUrl,
                headers: {
                    'User-Agent': this.headers['User-Agent'],
                    'Cookie': this.cookies,
                    'Referer': 'https://pro.m.jd.com/',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                },
                timeout: 10000
            });

            // 从页面HTML中提取actToken和actKey
            const html = response.data;

            // 尝试从页面脚本中提取参数 (多种匹配模式)
            const actTokenPatterns = [
                /actToken['":\s]*['"]([^'"]+)['"]/i,
                /"actToken"\s*:\s*"([^"]+)"/i,
                /'actToken'\s*:\s*'([^']+)'/i,
                /actToken\s*=\s*['"]([^'"]+)['"]/i
            ];

            const actKeyPatterns = [
                /actKey['":\s]*['"]([^'"]+)['"]/i,
                /"actKey"\s*:\s*"([^"]+)"/i,
                /'actKey'\s*:\s*'([^']+)'/i,
                /actKey\s*=\s*['"]([^'"]+)['"]/i
            ];

            // 尝试多种模式提取actToken
            for (const pattern of actTokenPatterns) {
                const match = html.match(pattern);
                if (match && match[1] && match[1].length > 5) {
                    this.actToken = match[1];
                    console.log(`✅ 从页面提取到actToken: ${this.actToken.substring(0, 10)}...`);
                    break;
                }
            }

            // 尝试多种模式提取actKey
            for (const pattern of actKeyPatterns) {
                const match = html.match(pattern);
                if (match && match[1] && match[1].length > 5) {
                    this.actKey = match[1];
                    console.log(`✅ 从页面提取到actKey: ${this.actKey.substring(0, 10)}...`);
                    break;
                }
            }

            // 如果没有提取到，打印调试信息
            if (!this.actToken) {
                console.log('🔍 [调试] 未能从页面提取到actToken，尝试查找相关字符串...');
                const tokenSearch = html.match(/token['":\s]*['"]([^'"]{10,})['"]/gi);
                if (tokenSearch) {
                    console.log('🔍 [调试] 找到的token相关字符串:', tokenSearch.slice(0, 3));
                }
            }

            if (!this.actKey) {
                console.log('🔍 [调试] 未能从页面提取到actKey，尝试查找相关字符串...');
                const keySearch = html.match(/key['":\s]*['"]([^'"]{10,})['"]/gi);
                if (keySearch) {
                    console.log('🔍 [调试] 找到的key相关字符串:', keySearch.slice(0, 3));
                }
            }

            return !!(this.actToken && this.actKey);

        } catch (error) {
            console.log('⚠️ 从页面获取活动信息失败，将尝试其他方式');
            return false;
        }
    }

    /**
     * 第一步：加载活动数据
     */
    async loadActivityData() {
        // 如果没有actToken和actKey，先尝试使用默认值或从其他方式获取
        if (!this.actToken || !this.actKey) {
            console.log('⚠️ 缺少actToken或actKey，尝试从响应中动态获取...');

            // 使用默认值或空值先尝试请求
            this.actToken = this.actToken || '';
            this.actKey = this.actKey || '';
        }

        // 获取jsToken用于x-api-eid-token
        const eidToken = this.jsToken || '';

        const bodyData = {
            token: this.actToken,
            commParams: {
                ubbLoc: 'ttf.lqzx',
                lid: '19_1601_50258_62859',
                client: 0,
                sdkToken: 'jdd01BWS7QQMQLJE3EDTDUO4CW2RMKFTVHBIBDCDZCV7MYXYYSJ6YIRCACJRP3YL6QNJP3YNB2OQGMP7IOND55OYF67XM2O2YTDAHUDKVNEQ01234567'
            },
            bizParams: {
                openChannel: 'jdAppHome',
                actKey: this.actKey,
                subLabel: ''
            }
        };

        // 获取h5st签名
        const h5st = await this.getH5st('comp_data_load', bodyData);

        const postData = {
            appid: 'day_day_reward',
            functionId: 'comp_data_load',
            loginType: '2',
            loginWQBiz: 'tttwxapp',
            body: JSON.stringify(bodyData),
            h5st: h5st,
            'x-api-eid-token': eidToken
        };

        try {
            // 调试信息：打印请求Cookie
            console.log('🔍 [调试] loadActivityData请求Cookie:', this.headers['Cookie'] ? this.headers['Cookie'].substring(0, 100) + '...' : '无Cookie');
            console.log('🔍 [调试] 请求参数:', {
                actToken: this.actToken ? this.actToken.substring(0, 10) + '...' : '空',
                actKey: this.actKey ? this.actKey.substring(0, 10) + '...' : '空',
                h5st: h5st ? h5st.substring(0, 20) + '...' : '空',
                eidToken: eidToken ? eidToken.substring(0, 20) + '...' : '空'
            });

            const response = await axios({
                method: 'POST',
                url: 'https://api.m.jd.com/client.action',
                headers: this.headers,
                data: qs.stringify(postData)
            });

            const result = response.data;

            if (result.success) {
                console.log('✅ 活动数据加载成功');

                // 从响应中提取所有必要参数
                const data = result.data || {};

                // 1. 提取actToken (如果响应中包含)
                if (data.actToken && !this.actToken) {
                    this.actToken = data.actToken;
                    console.log(`✅ 从响应提取到actToken: ${this.actToken.substring(0, 10)}...`);
                }

                // 2. 提取actKey (如果响应中包含)
                if (data.actKey && !this.actKey) {
                    this.actKey = data.actKey;
                    console.log(`✅ 从响应提取到actKey: ${this.actKey.substring(0, 10)}...`);
                }

                // 3. 从bizParams中提取actKey (备用方式)
                if (data.bizParams && data.bizParams.actKey && !this.actKey) {
                    this.actKey = data.bizParams.actKey;
                    console.log(`✅ 从bizParams提取到actKey: ${this.actKey.substring(0, 10)}...`);
                }

                // 4. 提取rewardReceiveKey
                const rewardProgressItems = data.rewardProgressItems || [];
                if (rewardProgressItems.length > 0) {
                    for (const item of rewardProgressItems) {
                        const rewardKey = item.rewardReceiveKey;
                        if (rewardKey) {
                            this.rewardReceiveKey = rewardKey;
                            console.log(`✅ 获取到rewardReceiveKey: ${rewardKey.substring(0, 20)}...`);
                            break;
                        }
                    }
                }

                // 5. 从其他可能的位置提取rewardReceiveKey
                if (!this.rewardReceiveKey && data.rewardReceiveKey) {
                    this.rewardReceiveKey = data.rewardReceiveKey;
                    console.log(`✅ 从data直接获取到rewardReceiveKey: ${this.rewardReceiveKey.substring(0, 20)}...`);
                }

                // 6. 从活动配置中提取参数 (如果存在)
                if (data.activityConfig) {
                    const config = data.activityConfig;
                    if (config.token && !this.actToken) {
                        this.actToken = config.token;
                        console.log(`✅ 从活动配置提取到actToken: ${this.actToken.substring(0, 10)}...`);
                    }
                    if (config.actKey && !this.actKey) {
                        this.actKey = config.actKey;
                        console.log(`✅ 从活动配置提取到actKey: ${this.actKey.substring(0, 10)}...`);
                    }
                }

                // 7. 打印完整响应结构用于调试 (可选)
                console.log('📋 响应数据结构预览:', JSON.stringify({
                    hasActToken: !!data.actToken,
                    hasActKey: !!data.actKey,
                    hasBizParams: !!data.bizParams,
                    hasRewardProgressItems: !!data.rewardProgressItems,
                    hasActivityConfig: !!data.activityConfig,
                    dataKeys: Object.keys(data)
                }, null, 2));

                // 检查是否成功获取到必要参数
                if (!this.rewardReceiveKey) {
                    console.log('⚠️ 未能获取到rewardReceiveKey，可能活动已结束或无可领取奖励');
                    // 不直接返回false，继续尝试其他方式
                }

                return true;
            } else {
                console.log(`❌ 活动数据加载失败: ${result.message || result.msg || '未知错误'}`);
                console.log('📋 完整错误响应:', JSON.stringify(result, null, 2));
                return false;
            }
        } catch (error) {
            return false;
        }
    }

    /**
     * 第二步：领取奖励
     */
    async claimReward() {
        if (!this.rewardReceiveKey) {
            return false;
        }

        // 获取jsToken用于x-api-eid-token
        const eidToken = this.jsToken || '';

        const bodyData = {
            token: this.actToken,
            fnCode: 'invoke',
            commParams: {
                longitude: '113.328752',
                latitude: '23.17921',
                ubbLoc: 'ttf.lqzx',
                lid: '19_1601_50258_62859',
                client: 0,
                sdkToken: 'jdd01BWS7QQMQLJE3EDTDUO4CW2RMKFTVHBIBDCDZCV7MYXYYSJ6YIRCACJRP3YL6QNJP3YNB2OQGMP7IOND55OYF67XM2O2YTDAHUDKVNEQ01234567'
            },
            bizParams: {
                rewardReceiveKey: this.rewardReceiveKey,
                openChannel: 'jdAppHome',
                actFlowCode: 'receiveReward',
                actKey: this.actKey,
                subLabel: ''
            }
        };

        // 获取h5st签名
        const h5st = await this.getH5st('comp_data_interact', bodyData);

        const postData = {
            appid: 'day_day_reward',
            functionId: 'comp_data_interact',
            loginType: '2',
            loginWQBiz: 'tttwxapp',
            body: JSON.stringify(bodyData),
            h5st: h5st,
            'x-api-eid-token': eidToken
        };

        try {
            // 调试信息：打印请求Cookie
            console.log('🔍 [调试] claimReward请求Cookie:', this.headers['Cookie'] ? this.headers['Cookie'].substring(0, 100) + '...' : '无Cookie');
            console.log('🔍 [调试] 请求参数:', {
                actToken: this.actToken ? this.actToken.substring(0, 10) + '...' : '空',
                actKey: this.actKey ? this.actKey.substring(0, 10) + '...' : '空',
                rewardReceiveKey: this.rewardReceiveKey ? this.rewardReceiveKey.substring(0, 20) + '...' : '空',
                h5st: h5st ? h5st.substring(0, 20) + '...' : '空',
                eidToken: eidToken ? eidToken.substring(0, 20) + '...' : '空'
            });

            const response = await axios({
                method: 'POST',
                url: 'https://api.m.jd.com/client.action',
                headers: this.headers,
                data: qs.stringify(postData)
            });

            const result = response.data;

            if (result.success) {
                console.log('🎉 奖励领取成功!');

                // 解析奖励信息
                const data = result.data || {};
                const rewardInfoList = data.rewardInfoList || [];

                if (rewardInfoList.length > 0) {
                    for (const reward of rewardInfoList) {
                        const couponInfo = reward.couponInfo || {};
                        if (couponInfo.couponDiscount) {
                            const discount = couponInfo.couponDiscount || 0;
                            const quota = couponInfo.couponQuota || 0;
                            const limitStr = couponInfo.couponLimitStr || '';
                            console.log(`🎫 获得优惠券: ${limitStr} ${discount}元券 (满${quota}元可用)`);
                        }
                    }
                } else {
                    console.log('🎁 奖励领取成功，但未获取到具体奖励信息');
                }

                return true;
            } else {
                console.log(`❌ 奖励领取失败: ${result.message || result.msg || '未知错误'}`);
                return false;
            }
        } catch (error) {
            console.log(`❌ 请求异常: ${error.message}`);
            return false;
        }
    }

    /**
     * 执行完整抽奖流程
     */
    async run() {
        // 第一步：加载活动数据
        if (!await this.loadActivityData()) {
            return false;
        }

        // 等待一下
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 第二步：领取奖励
        if (!await this.claimReward()) {
            return false;
        }

        return true;
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🎯 京东外卖抽奖活动开始...');

    if (!cookiesArr[0]) {
        console.log('【提示】请先获取京东账号cookie\n直接使用NobyDa的京东签到获取');
        return;
    }

    if (cookiesArr.length === 0) {
        console.log('❌ 未找到有效Cookie，请检查jdCookie.js文件');
        return;
    }

    console.log(`📝 共找到 ${cookiesArr.length} 个账号`);

    for (let i = 0; i < cookiesArr.length; i++) {
        const cookie = cookiesArr[i];
        console.log(`\n🔄 开始执行第 ${i + 1} 个账号...`);

        try {
            // 创建实例
            const lottery = new JDLottery();

            // 设置Cookie
            await lottery.setCookies(cookie);

            // 尝试自动获取活动参数
            console.log('🔍 尝试自动获取活动参数...');
            const autoGetParams = await lottery.getActivityInfo();

            // 如果自动获取失败，使用环境变量或手动设置
            if (!autoGetParams) {
                lottery.actToken = process.env.ACT_TOKEN || '';  // 可通过环境变量设置
                lottery.actKey = process.env.ACT_KEY || '';      // 可通过环境变量设置

                // 如果环境变量也没有，可以在这里手动设置
                if (!lottery.actToken) {
                    // lottery.actToken = 'your_act_token_here';  // 取消注释并填入真实值
                }
                if (!lottery.actKey) {
                    // lottery.actKey = 'your_act_key_here';      // 取消注释并填入真实值
                }
            }

            if (!lottery.actToken || !lottery.actKey) {
                console.log('⚠️ 缺少活动参数，尝试从loadActivityData响应中获取...');
                console.log('⚠️ 如果仍然失败，请设置环境变量: export ACT_TOKEN=xxx ACT_KEY=xxx');
                // 不跳过，继续执行，让loadActivityData尝试从响应中获取
            }

            console.log(`🎯 开始执行第 ${i + 1} 个账号的抽奖流程...`);

            // 执行抽奖
            const success = await lottery.run();

            if (success) {
                console.log(`🎉 第 ${i + 1} 个账号抽奖成功！`);
            } else {
                console.log(`❌ 第 ${i + 1} 个账号抽奖失败`);
            }

        } catch (error) {
            console.log(`❌ 第 ${i + 1} 个账号执行异常: ${error.message}`);
        }

        // 账号间延迟
        if (i < cookiesArr.length - 1) {
            console.log('⏰ 等待 3 秒后处理下一个账号...');
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }

    console.log('\n🎉 所有账号处理完成！');
}

// 直接运行脚本
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 脚本执行出错:', error.message);
        process.exit(1);
    });
}

/**
 * 使用说明:
 *
 * 1. 安装依赖:
 *    npm install axios qs
 *
 * 2. 配置Cookie (两种方式):
 *    方式一: 环境变量 (推荐)
 *    export JD_COOKIE="pt_key=xxx;pt_pin=xxx;&pt_key=yyy;pt_pin=yyy;"
 *
 *    方式二: 配置文件
 *    在 jdCookie.js 文件中配置你的京东Cookie
 *    格式: pt_key=xxx;pt_pin=xxx;
 *
 * 3. 设置活动参数 (两种方式):
 *    方式一: 环境变量 (推荐)
 *    export ACT_TOKEN="your_act_token" ACT_KEY="your_act_key"
 *
 *    方式二: 代码中设置
 *    在main函数中直接设置 lottery.actToken 和 lottery.actKey
 *
 * 4. 运行脚本:
 *    node jd_lottery.js
 *
 * 5. 重要提醒:
 *    - Cookie支持环境变量和文件两种获取方式
 *    - 推荐使用NobyDa的京东签到获取Cookie
 *    - h5st签名已集成自动获取 (http://10.0.0.189:3001/h5st)
 *    - h5st请求会自动传入账号Pin和活动h5st标识
 *    - x-api-eid-token已集成jsToken.js自动获取
 *    - actToken和actKey支持自动获取和手动设置
 *    - 请遵守相关服务条款
 */