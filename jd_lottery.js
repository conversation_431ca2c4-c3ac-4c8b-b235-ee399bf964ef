#!/usr/bin/env node
/**
 * 京东外卖抽奖活动脚本 - JavaScript版本
 * 基于抓包分析的完整实现，集成jsToken.js
 */

const axios = require('axios');
const qs = require('qs');
const { getJsToken } = require('./utils/jsToken.js');
const { setBaseCookie } = require('./utils/baseCookie.js');
const { BaseUtils } = require('./utils/baseUtils.js');
// 引入Cookie (支持环境变量和文件两种方式)
const jdCookieNode = process.env.JD_COOKIE ? process.env.JD_COOKIE.split('&') : require('./jdCookie.js');

let cookiesArr = [];
if (Array.isArray(jdCookieNode)) {
    cookiesArr = jdCookieNode;
} else if (typeof jdCookieNode === 'object') {
    Object.keys(jdCookieNode).forEach((item) => {
        cookiesArr.push(jdCookieNode[item]);
    });
} else {
    console.error('无法获取 cookies');
    process.exit(1);
}

// 加载依赖模块 (参照jdtry静默处理)
let SmashUtils = null;
try {
    const smashModule = require('./utils/smashUtils.js');
    SmashUtils = smashModule.SmashUtils;
} catch (error) {
    // 静默处理加载失败
}

class JDLottery {
    constructor() {
        // 初始化全局变量 (完全参照jdtry)
        this.initJdtryGlobals();

        // 固定请求头
        this.headers = {
            'Host': 'api.m.jd.com',
            'Accept': '*/*',
            'x-rp-client': 'h5_1.0.0',
            'Accept-Language': 'zh-cn',
            'Accept-Encoding': 'gzip, deflate, br',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://pro.m.jd.com',
            'User-Agent': 'jdapp;iPhone;15.1.14;;;M/5.0;appBuild/169836;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1748864332%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1;',
            'Referer': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
            'x-referer-page': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
            'Connection': 'keep-alive'
        };

        // 动态参数
        this.actToken = '';
        this.actKey = '';
        this.rewardReceiveKey = '';
        this.cookies = '';
        this.jsToken = '';
        this.uuid = '';
        this.smashUtils = null;
    }

    /**
     * 初始化jdtry标准的全局变量
     */
    initJdtryGlobals() {
        // 初始化全局$对象 (完全参照jdtry)
        global.$ = global.$ || {
            jsToken: '',
            UserName: '',
            cache: {},
            smashUtils: null,
            nickName: '',
            index: 0
        };

        // 初始化baseUtils (如果不存在)
        if (!global.baseUtils) {
            try {
                global.baseUtils = new BaseUtils();
            } catch (error) {
                // 静默处理
            }
        }
    }

    /**
     * 设置Cookie (完全参照jdtry方式)
     * @param {string} cookies - 用户Cookie字符串 (pt_key=xx;pt_pin=xx;格式)
     */
    async setCookies(cookies) {
        // 设置用户Cookie到实例和全局$
        this.cookies = cookies;
        global.$.UserName = this.extractPtPin(cookies);

        // 1. 初始化SmashUtils环境 (参照jdtry的initSmashUtils)
        await this.initSmashUtils(cookies);

        // 2. 初始化jsToken (参照jdtry的initJsToken)
        await this.initJsToken();

        // 3. 设置请求头Cookie (jdtry方式：直接使用用户Cookie)
        this.headers['Cookie'] = cookies;
    }

    /**
     * 提取pt_pin (参照jdtry方式)
     */
    extractPtPin(cookies) {
        const regex = /pt_pin=([^;]+)/;
        const match = cookies.match(regex);
        return match && match[1] ? decodeURIComponent(match[1]) : '';
    }

    /**
     * 初始化SmashUtils (完全参照jdtry的initSmashUtils)
     */
    async initSmashUtils(cookies) {
        if (!SmashUtils) return;

        try {
            const url = 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html';
            const userAgent = this.headers['User-Agent'];

            // 创建SmashUtils实例 (参照jdtry)
            this.smashUtils = new SmashUtils(url, cookies, userAgent);
            global.$.smashUtils = this.smashUtils;

            // 执行jdtry标准初始化流程
            this.smashUtils.getLocalData();
            this.smashUtils.getAppOs();
            this.smashUtils.getBlog();
            this.smashUtils.getFpv();

            // 获取设备信息
            await this.smashUtils.getInfo();

            // 设置joyya Cookie (风控)
            this.smashUtils.setjoyyaCookie('init');

            // 获取JR信息
            this.smashUtils.getJrInfo();

            // 初始化SmashUtils (参照jdtry的initial调用)
            await this.smashUtils.initial({
                appId: '50170_',
                debug: false,
                preRequest: true,
                onSign: (data) => data,
                onRequestTokenRemotely: async () => null
            });

        } catch (error) {
            // 静默处理错误
        }
    }

    /**
     * 初始化jsToken (完全参照jdtry的initJsToken)
     */
    async initJsToken() {
        try {
            if (!this.jsToken) {
                const userAgent = this.headers['User-Agent'];
                const url = 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html';
                const bizId = 'day_day_reward';

                this.jsToken = await getJsToken(userAgent, url, bizId);

                // 同步到全局$ (jdtry标准)
                global.$.jsToken = this.jsToken;
            }
        } catch (error) {
            // 静默处理错误
        }
    }



    /**
     * 获取h5st签名
     * @param {string} functionId - 功能ID
     * @param {object} bodyData - 请求体数据
     * @returns {string} h5st签名
     */
    async getH5st(functionId, bodyData) {
        try {
            let h5stParams;

            if (functionId === 'comp_data_load') {
                // comp_data_load请求的h5st参数
                h5stParams = {
                    appid: 'day_day_reward',
                    functionId: 'comp_data_load',
                    body: JSON.stringify(bodyData),
                    t: Date.now(),
                    clientVersion: '15.1.14',
                    client: 'ios',
                    uuid: this.getUuid(),
                    ua: this.headers['User-Agent'],
                    loginType: '2',
                    loginWQBiz: 'tttwxapp'
                };
            } else if (functionId === 'comp_data_interact') {
                // comp_data_interact请求的h5st参数
                h5stParams = {
                    appid: 'day_day_reward',
                    functionId: 'comp_data_interact',
                    body: JSON.stringify(bodyData),
                    t: Date.now(),
                    clientVersion: '15.1.14',
                    client: 'ios',
                    uuid: this.getUuid(),
                    ua: this.headers['User-Agent'],
                    loginType: '2',
                    loginWQBiz: 'tttwxapp'
                };
            } else {
                // 默认参数
                h5stParams = {
                    appid: 'day_day_reward',
                    functionId: functionId,
                    body: JSON.stringify(bodyData),
                    t: Date.now(),
                    clientVersion: '15.1.14',
                    client: 'ios',
                    uuid: this.getUuid(),
                    ua: this.headers['User-Agent']
                };
            }

            const response = await axios({
                method: 'POST',
                url: 'http://10.0.0.189:3001',
                headers: {
                    'Content-Type': 'application/json'
                },
                data: h5stParams,
                timeout: 5000
            });

            if (response.data && response.data.h5st) {
                return response.data.h5st;
            }

            return '';
        } catch (error) {
            return '';
        }
    }

    /**
     * 获取UUID
     */
    getUuid() {
        if (!this.uuid) {
            this.uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c === 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }
        return this.uuid;
    }

    /**
     * 获取活动基础信息 (从活动页面或初始API获取actToken和actKey)
     */
    async getActivityInfo() {
        try {
            console.log('🔍 正在获取活动基础信息...');

            // 方法1: 尝试访问活动页面获取基础参数
            const activityUrl = 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html';

            const response = await axios({
                method: 'GET',
                url: activityUrl,
                headers: {
                    'User-Agent': this.headers['User-Agent'],
                    'Cookie': this.cookies,
                    'Referer': 'https://pro.m.jd.com/',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                },
                timeout: 10000
            });

            // 从页面HTML中提取actToken和actKey
            const html = response.data;

            // 尝试从页面脚本中提取参数
            const actTokenMatch = html.match(/actToken['":\s]*['"]([^'"]+)['"]/i);
            const actKeyMatch = html.match(/actKey['":\s]*['"]([^'"]+)['"]/i);

            if (actTokenMatch && actTokenMatch[1]) {
                this.actToken = actTokenMatch[1];
                console.log(`✅ 从页面提取到actToken: ${this.actToken.substring(0, 10)}...`);
            }

            if (actKeyMatch && actKeyMatch[1]) {
                this.actKey = actKeyMatch[1];
                console.log(`✅ 从页面提取到actKey: ${this.actKey.substring(0, 10)}...`);
            }

            return !!(this.actToken && this.actKey);

        } catch (error) {
            console.log('⚠️ 从页面获取活动信息失败，将尝试其他方式');
            return false;
        }
    }

    /**
     * 第一步：加载活动数据
     */
    async loadActivityData() {
        // 如果没有actToken和actKey，先尝试使用默认值或从其他方式获取
        if (!this.actToken || !this.actKey) {
            console.log('⚠️ 缺少actToken或actKey，尝试从响应中动态获取...');

            // 使用默认值或空值先尝试请求
            this.actToken = this.actToken || '';
            this.actKey = this.actKey || '';
        }

        // 获取jsToken用于x-api-eid-token
        const eidToken = this.jsToken || '';

        const bodyData = {
            token: this.actToken,
            commParams: {
                ubbLoc: 'ttf.lqzx',
                lid: '19_1601_50258_62859',
                client: 0,
                sdkToken: 'jdd01BWS7QQMQLJE3EDTDUO4CW2RMKFTVHBIBDCDZCV7MYXYYSJ6YIRCACJRP3YL6QNJP3YNB2OQGMP7IOND55OYF67XM2O2YTDAHUDKVNEQ01234567'
            },
            bizParams: {
                openChannel: 'jdAppHome',
                actKey: this.actKey,
                subLabel: ''
            }
        };

        // 获取h5st签名
        const h5st = await this.getH5st('comp_data_load', bodyData);

        const postData = {
            appid: 'day_day_reward',
            functionId: 'comp_data_load',
            loginType: '2',
            loginWQBiz: 'tttwxapp',
            body: JSON.stringify(bodyData),
            h5st: h5st,
            'x-api-eid-token': eidToken
        };

        try {
            const response = await axios({
                method: 'POST',
                url: 'https://api.m.jd.com/client.action',
                headers: this.headers,
                data: qs.stringify(postData)
            });

            const result = response.data;

            if (result.success) {
                console.log('✅ 活动数据加载成功');

                // 从响应中提取所有必要参数
                const data = result.data || {};

                // 1. 提取actToken (如果响应中包含)
                if (data.actToken && !this.actToken) {
                    this.actToken = data.actToken;
                    console.log(`✅ 从响应提取到actToken: ${this.actToken.substring(0, 10)}...`);
                }

                // 2. 提取actKey (如果响应中包含)
                if (data.actKey && !this.actKey) {
                    this.actKey = data.actKey;
                    console.log(`✅ 从响应提取到actKey: ${this.actKey.substring(0, 10)}...`);
                }

                // 3. 从bizParams中提取actKey (备用方式)
                if (data.bizParams && data.bizParams.actKey && !this.actKey) {
                    this.actKey = data.bizParams.actKey;
                    console.log(`✅ 从bizParams提取到actKey: ${this.actKey.substring(0, 10)}...`);
                }

                // 4. 提取rewardReceiveKey
                const rewardProgressItems = data.rewardProgressItems || [];
                if (rewardProgressItems.length > 0) {
                    for (const item of rewardProgressItems) {
                        const rewardKey = item.rewardReceiveKey;
                        if (rewardKey) {
                            this.rewardReceiveKey = rewardKey;
                            console.log(`✅ 获取到rewardReceiveKey: ${rewardKey.substring(0, 20)}...`);
                            break;
                        }
                    }
                }

                // 5. 从其他可能的位置提取rewardReceiveKey
                if (!this.rewardReceiveKey && data.rewardReceiveKey) {
                    this.rewardReceiveKey = data.rewardReceiveKey;
                    console.log(`✅ 从data直接获取到rewardReceiveKey: ${this.rewardReceiveKey.substring(0, 20)}...`);
                }

                // 6. 从活动配置中提取参数 (如果存在)
                if (data.activityConfig) {
                    const config = data.activityConfig;
                    if (config.token && !this.actToken) {
                        this.actToken = config.token;
                        console.log(`✅ 从活动配置提取到actToken: ${this.actToken.substring(0, 10)}...`);
                    }
                    if (config.actKey && !this.actKey) {
                        this.actKey = config.actKey;
                        console.log(`✅ 从活动配置提取到actKey: ${this.actKey.substring(0, 10)}...`);
                    }
                }

                // 7. 打印完整响应结构用于调试 (可选)
                console.log('📋 响应数据结构预览:', JSON.stringify({
                    hasActToken: !!data.actToken,
                    hasActKey: !!data.actKey,
                    hasBizParams: !!data.bizParams,
                    hasRewardProgressItems: !!data.rewardProgressItems,
                    hasActivityConfig: !!data.activityConfig,
                    dataKeys: Object.keys(data)
                }, null, 2));

                // 检查是否成功获取到必要参数
                if (!this.rewardReceiveKey) {
                    console.log('⚠️ 未能获取到rewardReceiveKey，可能活动已结束或无可领取奖励');
                    // 不直接返回false，继续尝试其他方式
                }

                return true;
            } else {
                console.log(`❌ 活动数据加载失败: ${result.message || result.msg || '未知错误'}`);
                console.log('📋 完整错误响应:', JSON.stringify(result, null, 2));
                return false;
            }
        } catch (error) {
            return false;
        }
    }

    /**
     * 第二步：领取奖励
     */
    async claimReward() {
        if (!this.rewardReceiveKey) {
            return false;
        }

        // 获取jsToken用于x-api-eid-token
        const eidToken = this.jsToken || '';

        const bodyData = {
            token: this.actToken,
            fnCode: 'invoke',
            commParams: {
                longitude: '113.328752',
                latitude: '23.17921',
                ubbLoc: 'ttf.lqzx',
                lid: '19_1601_50258_62859',
                client: 0,
                sdkToken: 'jdd01BWS7QQMQLJE3EDTDUO4CW2RMKFTVHBIBDCDZCV7MYXYYSJ6YIRCACJRP3YL6QNJP3YNB2OQGMP7IOND55OYF67XM2O2YTDAHUDKVNEQ01234567'
            },
            bizParams: {
                rewardReceiveKey: this.rewardReceiveKey,
                openChannel: 'jdAppHome',
                actFlowCode: 'receiveReward',
                actKey: this.actKey,
                subLabel: ''
            }
        };

        // 获取h5st签名
        const h5st = await this.getH5st('comp_data_interact', bodyData);

        const postData = {
            appid: 'day_day_reward',
            functionId: 'comp_data_interact',
            loginType: '2',
            loginWQBiz: 'tttwxapp',
            body: JSON.stringify(bodyData),
            h5st: h5st,
            'x-api-eid-token': eidToken
        };

        try {
            const response = await axios({
                method: 'POST',
                url: 'https://api.m.jd.com/client.action',
                headers: this.headers,
                data: qs.stringify(postData)
            });

            const result = response.data;

            if (result.success) {
                console.log('🎉 奖励领取成功!');

                // 解析奖励信息
                const data = result.data || {};
                const rewardInfoList = data.rewardInfoList || [];

                if (rewardInfoList.length > 0) {
                    for (const reward of rewardInfoList) {
                        const couponInfo = reward.couponInfo || {};
                        if (couponInfo.couponDiscount) {
                            const discount = couponInfo.couponDiscount || 0;
                            const quota = couponInfo.couponQuota || 0;
                            const limitStr = couponInfo.couponLimitStr || '';
                            console.log(`🎫 获得优惠券: ${limitStr} ${discount}元券 (满${quota}元可用)`);
                        }
                    }
                } else {
                    console.log('🎁 奖励领取成功，但未获取到具体奖励信息');
                }

                return true;
            } else {
                console.log(`❌ 奖励领取失败: ${result.message || result.msg || '未知错误'}`);
                return false;
            }
        } catch (error) {
            console.log(`❌ 请求异常: ${error.message}`);
            return false;
        }
    }

    /**
     * 执行完整抽奖流程
     */
    async run() {
        // 第一步：加载活动数据
        if (!await this.loadActivityData()) {
            return false;
        }

        // 等待一下
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 第二步：领取奖励
        if (!await this.claimReward()) {
            return false;
        }

        return true;
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🎯 京东外卖抽奖活动开始...');

    if (!cookiesArr[0]) {
        console.log('【提示】请先获取京东账号cookie\n直接使用NobyDa的京东签到获取');
        return;
    }

    if (cookiesArr.length === 0) {
        console.log('❌ 未找到有效Cookie，请检查jdCookie.js文件');
        return;
    }

    console.log(`📝 共找到 ${cookiesArr.length} 个账号`);

    for (let i = 0; i < cookiesArr.length; i++) {
        const cookie = cookiesArr[i];
        console.log(`\n🔄 开始执行第 ${i + 1} 个账号...`);

        try {
            // 创建实例
            const lottery = new JDLottery();

            // 设置Cookie
            await lottery.setCookies(cookie);

            // 尝试自动获取活动参数
            console.log('🔍 尝试自动获取活动参数...');
            const autoGetParams = await lottery.getActivityInfo();

            // 如果自动获取失败，使用环境变量或手动设置
            if (!autoGetParams) {
                lottery.actToken = process.env.ACT_TOKEN || '';  // 可通过环境变量设置
                lottery.actKey = process.env.ACT_KEY || '';      // 可通过环境变量设置

                // 如果环境变量也没有，可以在这里手动设置
                if (!lottery.actToken) {
                    // lottery.actToken = 'your_act_token_here';  // 取消注释并填入真实值
                }
                if (!lottery.actKey) {
                    // lottery.actKey = 'your_act_key_here';      // 取消注释并填入真实值
                }
            }

            if (!lottery.actToken || !lottery.actKey) {
                console.log('⚠️ 缺少活动参数，尝试从loadActivityData响应中获取...');
                console.log('⚠️ 如果仍然失败，请设置环境变量: export ACT_TOKEN=xxx ACT_KEY=xxx');
                // 不跳过，继续执行，让loadActivityData尝试从响应中获取
            }

            console.log(`🎯 开始执行第 ${i + 1} 个账号的抽奖流程...`);

            // 执行抽奖
            const success = await lottery.run();

            if (success) {
                console.log(`🎉 第 ${i + 1} 个账号抽奖成功！`);
            } else {
                console.log(`❌ 第 ${i + 1} 个账号抽奖失败`);
            }

        } catch (error) {
            console.log(`❌ 第 ${i + 1} 个账号执行异常: ${error.message}`);
        }

        // 账号间延迟
        if (i < cookiesArr.length - 1) {
            console.log('⏰ 等待 3 秒后处理下一个账号...');
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }

    console.log('\n🎉 所有账号处理完成！');
}

// 直接运行脚本
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 脚本执行出错:', error.message);
        process.exit(1);
    });
}

/**
 * 使用说明:
 *
 * 1. 安装依赖:
 *    npm install axios qs
 *
 * 2. 配置Cookie (两种方式):
 *    方式一: 环境变量 (推荐)
 *    export JD_COOKIE="pt_key=xxx;pt_pin=xxx;&pt_key=yyy;pt_pin=yyy;"
 *
 *    方式二: 配置文件
 *    在 jdCookie.js 文件中配置你的京东Cookie
 *    格式: pt_key=xxx;pt_pin=xxx;
 *
 * 3. 设置活动参数 (两种方式):
 *    方式一: 环境变量 (推荐)
 *    export ACT_TOKEN="your_act_token" ACT_KEY="your_act_key"
 *
 *    方式二: 代码中设置
 *    在main函数中直接设置 lottery.actToken 和 lottery.actKey
 *
 * 4. 运行脚本:
 *    node jd_lottery.js
 *
 * 5. 重要提醒:
 *    - Cookie支持环境变量和文件两种获取方式
 *    - 推荐使用NobyDa的京东签到获取Cookie
 *    - h5st签名已集成自动获取 (http://10.0.0.189:3001)
 *    - x-api-eid-token已集成jsToken.js自动获取
 *    - actToken和actKey需要手动设置
 *    - 请遵守相关服务条款
 */