#!/usr/bin/env node
/**
 * 京东外卖抽奖活动脚本 - JavaScript版本
 * 基于抓包分析的完整实现，集成jsToken.js
 */

const axios = require('axios');
const qs = require('qs');
const { getJsToken } = require('./utils/jsToken.js');

class JDLottery {
    constructor() {
        // 固定请求头
        this.headers = {
            'Host': 'api.m.jd.com',
            'Accept': '*/*',
            'x-rp-client': 'h5_1.0.0',
            'Accept-Language': 'zh-cn',
            'Accept-Encoding': 'gzip, deflate, br',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://pro.m.jd.com',
            'User-Agent': 'jdapp;iPhone;15.1.14;;;M/5.0;appBuild/169836;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1748864332%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1;',
            'Referer': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
            'x-referer-page': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
            'Connection': 'keep-alive'
        };

        // 动态参数
        this.actToken = '';
        this.actKey = '';
        this.rewardReceiveKey = '';
        this.cookies = '';
        this.jsToken = '';
    }

    /**
     * 设置Cookie
     * @param {string} cookies - Cookie字符串
     */
    setCookies(cookies) {
        this.cookies = cookies;
        this.headers['Cookie'] = cookies;
    }

    /**
     * 获取jsToken用于x-api-eid-token参数
     */
    async getApiEidToken() {
        try {
            console.log('🔑 正在获取jsToken...');

            const userAgent = this.headers['User-Agent'];
            const url = 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html';
            const bizId = 'day_day_reward';

            this.jsToken = await getJsToken(userAgent, url, bizId);

            if (this.jsToken) {
                console.log(`✅ jsToken获取成功: ${this.jsToken.substring(0, 30)}...`);
                return this.jsToken;
            } else {
                console.log('❌ jsToken获取失败');
                return '';
            }
        } catch (error) {
            console.log(`❌ jsToken获取异常: ${error.message}`);
            return '';
        }
    }

    /**
     * 第一步：加载活动数据
     */
    async loadActivityData() {
        console.log('🔄 正在加载活动数据...');

        if (!this.actToken) {
            console.log('❌ 缺少 actToken');
            return false;
        }

        if (!this.actKey) {
            console.log('❌ 缺少 actKey');
            return false;
        }

        // 获取jsToken用于x-api-eid-token
        const eidToken = await this.getApiEidToken();

        const bodyData = {
            token: this.actToken,
            commParams: {
                ubbLoc: 'ttf.lqzx',
                lid: '19_1601_50258_62859',
                client: 0,
                sdkToken: 'jdd01BWS7QQMQLJE3EDTDUO4CW2RMKFTVHBIBDCDZCV7MYXYYSJ6YIRCACJRP3YL6QNJP3YNB2OQGMP7IOND55OYF67XM2O2YTDAHUDKVNEQ01234567'
            },
            bizParams: {
                openChannel: 'jdAppHome',
                actKey: this.actKey,
                subLabel: ''
            }
        };

        const postData = {
            appid: 'day_day_reward',
            functionId: 'comp_data_load',
            loginType: '2',
            loginWQBiz: 'tttwxapp',
            body: JSON.stringify(bodyData),
            h5st: '', // 需要实现h5st算法
            'x-api-eid-token': eidToken
        };

        try {
            const response = await axios({
                method: 'POST',
                url: 'https://api.m.jd.com/client.action',
                headers: this.headers,
                data: qs.stringify(postData)
            });

            const result = response.data;

            if (result.success) {
                console.log('✅ 活动数据加载成功');

                // 提取rewardReceiveKey
                const data = result.data || {};
                const rewardProgressItems = data.rewardProgressItems || [];

                if (rewardProgressItems.length > 0) {
                    for (const item of rewardProgressItems) {
                        const rewardKey = item.rewardReceiveKey;
                        if (rewardKey) {
                            this.rewardReceiveKey = rewardKey;
                            console.log(`📝 获取到 rewardReceiveKey: ${rewardKey.substring(0, 30)}...`);
                            break;
                        }
                    }
                }

                return true;
            } else {
                console.log(`❌ 活动数据加载失败: ${result.message || '未知错误'}`);
                return false;
            }
        } catch (error) {
            console.log(`❌ 请求异常: ${error.message}`);
            return false;
        }
    }

    /**
     * 第二步：领取奖励
     */
    async claimReward() {
        console.log('🎁 正在领取奖励...');

        if (!this.rewardReceiveKey) {
            console.log('❌ 缺少 rewardReceiveKey，请先加载活动数据');
            return false;
        }

        // 获取jsToken用于x-api-eid-token
        const eidToken = await this.getApiEidToken();

        const bodyData = {
            token: this.actToken,
            fnCode: 'invoke',
            commParams: {
                longitude: '113.328752',
                latitude: '23.17921',
                ubbLoc: 'ttf.lqzx',
                lid: '19_1601_50258_62859',
                client: 0,
                sdkToken: 'jdd01BWS7QQMQLJE3EDTDUO4CW2RMKFTVHBIBDCDZCV7MYXYYSJ6YIRCACJRP3YL6QNJP3YNB2OQGMP7IOND55OYF67XM2O2YTDAHUDKVNEQ01234567'
            },
            bizParams: {
                rewardReceiveKey: this.rewardReceiveKey,
                openChannel: 'jdAppHome',
                actFlowCode: 'receiveReward',
                actKey: this.actKey,
                subLabel: ''
            }
        };

        const postData = {
            appid: 'day_day_reward',
            functionId: 'comp_data_interact',
            loginType: '2',
            loginWQBiz: 'tttwxapp',
            body: JSON.stringify(bodyData),
            h5st: '', // 需要实现h5st算法
            'x-api-eid-token': eidToken
        };

        try {
            const response = await axios({
                method: 'POST',
                url: 'https://api.m.jd.com/client.action',
                headers: this.headers,
                data: qs.stringify(postData)
            });

            const result = response.data;

            if (result.success) {
                console.log('🎉 奖励领取成功!');

                // 解析奖励信息
                const data = result.data || {};
                const rewardInfoList = data.rewardInfoList || [];

                if (rewardInfoList.length > 0) {
                    for (const reward of rewardInfoList) {
                        const couponInfo = reward.couponInfo || {};
                        if (couponInfo.couponDiscount) {
                            const discount = couponInfo.couponDiscount || 0;
                            const quota = couponInfo.couponQuota || 0;
                            const limitStr = couponInfo.couponLimitStr || '';
                            console.log(`🎫 获得优惠券: ${limitStr} ${discount}元券 (满${quota}元可用)`);
                        }
                    }
                }

                return true;
            } else {
                console.log(`❌ 奖励领取失败: ${result.message || '未知错误'}`);
                return false;
            }
        } catch (error) {
            console.log(`❌ 请求异常: ${error.message}`);
            return false;
        }
    }

    /**
     * 执行完整抽奖流程
     */
    async run() {
        console.log('🚀 开始执行抽奖...');

        // 第一步：加载活动数据
        if (!await this.loadActivityData()) {
            return false;
        }

        // 等待一下
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 第二步：领取奖励
        if (!await this.claimReward()) {
            return false;
        }

        console.log('✅ 抽奖完成!');
        return true;
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('='.repeat(50));
    console.log('京东外卖抽奖活动脚本 - JavaScript版本');
    console.log('='.repeat(50));

    // 创建实例
    const lottery = new JDLottery();

    // 设置Cookie (需要用户提供)
    const cookies = `
        sdtoken=AAbEsBpEIOVjqTAKCQtvQu17uCPhmHjpU5ZKh7k7xAhmuEVsq-JkZ3luf9QG1sOt9CtLppgrhk7vPWXToHJDTbWrkHyYE2m6k47jzPU2aQB4b4YBPgK0-CaghBA;
        pt_key=app_openAAJoPXKMADDCQtjfDN1ei7zngTtGDgG6HrDf_Ei_YIp2N7tg8fIrrbpS6Xl1TEFJsD_jiAXkIW4;
        pt_pin=jd_4b25e12eb2177;
        3AB9D23F7A4B3CSS=jdd03IFRDI7JL55ZJCFWHCCFV35GLGY4BZH4LAVWYCMXUV2W6AU3YZZ5TWUYNRD565WJQS4XHZWCHFZUN5WOPAAU7UVYUZMAAAAMXGBYA7QAAAAAADLU6IBRAYBHW44X
    `.trim();

    lottery.setCookies(cookies);

    // 设置必要参数 (需要从响应中获取)
    lottery.actToken = 'pVQSJCqPp3oXPfH9Y28Tv';  // 需要替换为真实值
    lottery.actKey = 'iadem13cpdiof3vtyykmr';     // 需要替换为真实值

    // 执行抽奖
    const success = await lottery.run();

    if (success) {
        console.log('\n🎉 抽奖成功完成!');
    } else {
        console.log('\n❌ 抽奖失败');
    }
}

// 导出类和主函数
module.exports = { JDLottery, main };

// 如果直接运行此文件
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 脚本执行出错:', error.message);
        process.exit(1);
    });
}

/**
 * 使用说明:
 *
 * 1. 安装依赖:
 *    npm install axios qs
 *
 * 2. 设置Cookie:
 *    将main函数中的cookies变量替换为你的真实Cookie
 *
 * 3. 设置参数:
 *    设置actToken和actKey参数
 *
 * 4. 运行脚本:
 *    node jd_lottery.js
 *
 * 5. 重要提醒:
 *    - h5st参数需要实现完整算法，当前为空
 *    - x-api-eid-token参数已集成jsToken.js自动获取
 *    - Cookie需要定期更新
 *    - 请遵守相关服务条款
 */