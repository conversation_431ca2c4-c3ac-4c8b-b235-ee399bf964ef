# 🔧 h5st签名增强完成

## ✅ **h5st签名增强总结**

已成功修改h5st签名请求，增加了账号Pin和活动h5st标识参数，并更新了请求URL。

## 🔧 **核心修改内容**

### **1. 更新h5st请求URL**

```javascript
// ❌ 修改前
url: 'http://**********:3001'

// ✅ 修改后
url: 'http://**********:3001/h5st'
```

### **2. 增加账号Pin参数**

```javascript
// 获取用户Pin (从全局$或Cookie中提取)
const userPin = global.$.UserName || this.extractPtPin(this.cookies) || '';

// 在h5stParams中增加pin参数
h5stParams = {
    // ... 其他参数
    pin: userPin,  // 增加账号Pin
    // ... 其他参数
};
```

### **3. 增加活动h5st标识**

```javascript
if (functionId === 'comp_data_load') {
    h5stParams = {
        // ... 其他参数
        h5st: 'comp_data_load_h5st'  // 活动对应的h5st标识
    };
} else if (functionId === 'comp_data_interact') {
    h5stParams = {
        // ... 其他参数
        h5st: 'comp_data_interact_h5st'  // 活动对应的h5st标识
    };
} else {
    h5stParams = {
        // ... 其他参数
        h5st: `${functionId}_h5st`  // 动态生成h5st标识
    };
}
```

### **4. 增强调试信息**

```javascript
// 调试信息：打印h5st请求参数
console.log('🔍 [调试] h5st请求参数:', {
    functionId: functionId,
    appid: h5stParams.appid,
    pin: h5stParams.pin || '空',           // 新增：显示账号Pin
    h5st: h5stParams.h5st || '空',         // 新增：显示h5st标识
    uuid: h5stParams.uuid ? h5stParams.uuid.substring(0, 10) + '...' : '空',
    bodyLength: h5stParams.body ? h5stParams.body.length : 0
});
```

## 📊 **完整的h5st参数结构**

### **comp_data_load请求参数**

```javascript
{
    appid: 'day_day_reward',
    functionId: 'comp_data_load',
    body: '{"token":"xxx","commParams":{...},"bizParams":{...}}',
    t: 1704268523123,
    clientVersion: '15.1.14',
    client: 'ios',
    uuid: '12345678-1234-4xxx-yxxx-xxxxxxxxxxxx',
    ua: 'jdapp;iPhone;15.1.14;...',
    loginType: '2',
    loginWQBiz: 'tttwxapp',
    pin: 'jd_username',                    // 新增：账号Pin
    h5st: 'comp_data_load_h5st'           // 新增：活动h5st标识
}
```

### **comp_data_interact请求参数**

```javascript
{
    appid: 'day_day_reward',
    functionId: 'comp_data_interact',
    body: '{"token":"xxx","fnCode":"invoke","commParams":{...},"bizParams":{...}}',
    t: 1704268523456,
    clientVersion: '15.1.14',
    client: 'ios',
    uuid: '12345678-1234-4xxx-yxxx-xxxxxxxxxxxx',
    ua: 'jdapp;iPhone;15.1.14;...',
    loginType: '2',
    loginWQBiz: 'tttwxapp',
    pin: 'jd_username',                    // 新增：账号Pin
    h5st: 'comp_data_interact_h5st'       // 新增：活动h5st标识
}
```

## 🔍 **调试信息输出示例**

### **正常请求时的调试输出**

```
🔍 [调试] h5st请求参数: {
  functionId: "comp_data_load",
  appid: "day_day_reward",
  pin: "jd_username",
  h5st: "comp_data_load_h5st",
  uuid: "12345678-1...",
  bodyLength: 245
}
🔍 [调试] h5st签名获取成功: 20250103154523123456789012...
```

### **缺少Pin时的调试输出**

```
🔍 [调试] h5st请求参数: {
  functionId: "comp_data_load",
  appid: "day_day_reward",
  pin: "空",
  h5st: "comp_data_load_h5st",
  uuid: "12345678-1...",
  bodyLength: 245
}
```

## 🎯 **h5st服务端要求**

### **请求格式**

```
POST http://**********:3001/h5st
Content-Type: application/json

{
    "appid": "day_day_reward",
    "functionId": "comp_data_load",
    "body": "{...}",
    "t": 1704268523123,
    "clientVersion": "15.1.14",
    "client": "ios",
    "uuid": "12345678-1234-4xxx-yxxx-xxxxxxxxxxxx",
    "ua": "jdapp;iPhone;15.1.14;...",
    "loginType": "2",
    "loginWQBiz": "tttwxapp",
    "pin": "jd_username",
    "h5st": "comp_data_load_h5st"
}
```

### **响应格式**

```json
{
    "h5st": "20250103154523123456789012345678901234567890"
}
```

## 🔧 **Pin获取机制**

### **Pin获取优先级**

1. **global.$.UserName** - 从全局变量获取（setCookies时设置）
2. **this.extractPtPin(this.cookies)** - 从当前Cookie中提取
3. **空字符串** - 如果都获取不到则为空

### **Pin提取逻辑**

```javascript
extractPtPin(cookies) {
    const regex = /pt_pin=([^;]+)/;
    const match = cookies.match(regex);
    return match && match[1] ? decodeURIComponent(match[1]) : '';
}
```

## 🚀 **使用方式**

### **h5st服务端配置**

确保h5st签名服务运行在 `http://**********:3001/h5st` 并能处理新的参数格式。

### **客户端使用**

```bash
# 正常运行，会自动传入Pin和h5st标识
export JD_COOKIE="pt_key=xxx;pt_pin=xxx;"
node jd_lottery.js
```

### **服务端处理示例**

```javascript
// h5st服务端需要处理的参数
app.post('/h5st', (req, res) => {
    const {
        appid,
        functionId,
        body,
        t,
        clientVersion,
        client,
        uuid,
        ua,
        loginType,
        loginWQBiz,
        pin,        // 新增：账号Pin
        h5st        // 新增：活动h5st标识
    } = req.body;
    
    // 根据pin和h5st标识生成对应的签名
    const signature = generateH5st({
        pin,
        h5st,
        // ... 其他参数
    });
    
    res.json({ h5st: signature });
});
```

## 🎯 **增强优势**

### **✅ 账号级别签名**

1. **个性化签名** - 每个账号使用独立的Pin生成签名
2. **安全性提升** - 避免多账号使用相同签名
3. **风控友好** - 符合京东的账号隔离要求

### **✅ 活动级别标识**

1. **活动区分** - 不同API使用不同的h5st标识
2. **签名精确** - 根据具体活动生成对应签名
3. **扩展性好** - 支持多种活动类型

### **✅ 调试增强**

1. **参数可见** - 清晰显示Pin和h5st标识
2. **问题定位** - 快速发现Pin缺失或h5st错误
3. **状态监控** - 实时监控签名请求状态

## 🎉 **h5st增强完成总结**

### **✅ 完成的增强功能**

1. **URL更新** - 修改为 `/h5st` 端点
2. **Pin参数** - 自动提取并传入账号Pin
3. **h5st标识** - 根据functionId设置对应标识
4. **调试增强** - 显示Pin和h5st标识状态
5. **文档更新** - 更新使用说明和注释

### **✅ 技术特性**

1. **向后兼容** - 保持原有功能不变
2. **自动化** - Pin自动提取，无需手动设置
3. **灵活性** - 支持多种functionId和活动类型
4. **可靠性** - 完善的错误处理和调试信息
5. **标准化** - 符合抓包分析的参数要求

### **🎯 实际效果**

现在h5st签名请求会携带：
- ✅ **账号Pin** - 实现账号级别的个性化签名
- ✅ **活动标识** - 区分不同API的签名需求
- ✅ **完整参数** - 包含所有必要的签名参数
- ✅ **调试信息** - 便于问题排查和监控

这确保了h5st签名服务能够根据具体账号和活动生成准确的签名，提高了脚本的成功率和安全性！
