#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东外卖抽奖活动完整自动化脚本
基于TXT和HAR文件分析的最终版本

功能特点:
1. 自动获取活动配置
2. 智能参数提取和处理
3. 完整的错误处理机制
4. 支持多活动实例
5. 详细的日志输出
6. 配置文件支持
"""

import requests
import json
import time
import hashlib
import random
import urllib.parse
import re
import os
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('jd_lottery.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class JDLotteryConfig:
    """配置管理类"""

    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config = self.load_config()

    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            "user_agent": "jdapp;iPhone;15.1.14;;;M/5.0;appBuild/169836;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1748864332%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1;",
            "base_url": "https://api.m.jd.com/client.action",
            "referer": "https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html",
            "origin": "https://pro.m.jd.com",
            "appid": "day_day_reward",
            "login_type": "2",
            "login_wq_biz": "tttwxapp",
            "location": {
                "longitude": "113.328752",
                "latitude": "23.17921",
                "lid": "19_1601_50258_62859",
                "ubb_loc": "ttf.lqzx"
            },
            "request_timeout": 30,
            "retry_times": 3,
            "retry_delay": 2
        }

        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
                    logger.info(f"已加载配置文件: {self.config_file}")
            except Exception as e:
                logger.warning(f"配置文件加载失败，使用默认配置: {e}")
        else:
            self.save_config(default_config)
            logger.info(f"已创建默认配置文件: {self.config_file}")

        return default_config

    def save_config(self, config: Dict[str, Any]):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"配置文件保存失败: {e}")

    def get(self, key: str, default=None):
        """获取配置项"""
        return self.config.get(key, default)

    def set(self, key: str, value: Any):
        """设置配置项"""
        self.config[key] = value
        self.save_config(self.config)

class H5STGenerator:
    """H5ST参数生成器 - 需要实现完整算法"""

    def __init__(self):
        # 移除简化算法，需要实现完整的h5st生成逻辑
        pass

    def generate(self, function_id: str, body: str, timestamp: Optional[int] = None) -> str:
        """
        生成h5st参数

        重要提醒: 此处需要实现完整的h5st算法
        当前返回空字符串，实际使用时需要：
        1. 逆向分析京东的h5st生成算法
        2. 实现完整的签名逻辑
        3. 包含设备指纹、时间戳等参数
        """
        logger.warning("⚠️ H5ST算法未实现，需要完整逆向后替换此方法")
        return ""  # 返回空字符串，需要实现完整算法

class JDLotteryBot:
    """京东抽奖机器人主类"""

    def __init__(self, cookies: str = ""):
        self.session = requests.Session()
        self.h5st_generator = H5STGenerator()

        # 全局Cookie (无需提取)
        self.cookies = cookies

        # 需要从第一步响应获取的动态参数
        self.act_token = ""  # 从第一步响应获取
        self.act_key = ""    # 从第一步响应获取
        self.reward_receive_key = ""  # 从第一步响应获取

        # 活动信息
        self.activity_data = {}

        # 设置请求头和Cookie
        self._setup_headers()
        if cookies:
            self._parse_cookies(cookies)

    def _setup_headers(self):
        """设置固定请求头"""
        self.session.headers.update({
            'Host': 'api.m.jd.com',
            'Accept': '*/*',
            'x-rp-client': 'h5_1.0.0',
            'Accept-Language': 'zh-cn',
            'Accept-Encoding': 'gzip, deflate, br',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://pro.m.jd.com',
            'User-Agent': 'jdapp;iPhone;15.1.14;;;M/5.0;appBuild/169836;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1748864332%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D;Mozilla/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1;',
            'Referer': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
            'x-referer-page': 'https://pro.m.jd.com/mall/active/VAjs3vpayA513UwxL5XC4eGBXqY/index.html',
            'Connection': 'keep-alive'
        })

    def _parse_cookies(self, cookies: str):
        """解析并设置Cookie"""
        cookie_dict = {}
        for cookie in cookies.split(';'):
            cookie = cookie.strip()
            if '=' in cookie:
                key, value = cookie.split('=', 1)
                cookie_dict[key.strip()] = value.strip()
        self.session.cookies.update(cookie_dict)

    def set_cookies(self, cookies: str):
        """
        设置Cookie (固定活动脚本，只需要Cookie)
        :param cookies: Cookie字符串
        """
        logger.info("🔐 设置Cookie...")
        self.cookies = cookies
        self._parse_cookies(cookies)
        logger.info("✅ Cookie设置完成")

    def auto_extract_from_files(self, txt_files: List[str] = None, har_file: str = None):
        """
        从抓包文件自动提取认证信息
        :param txt_files: TXT文件列表
        :param har_file: HAR文件路径
        """
        logger.info("🔍 自动提取认证信息...")

        # 从TXT文件提取
        if txt_files:
            for txt_file in txt_files:
                if os.path.exists(txt_file):
                    self._extract_from_txt(txt_file)

        # 从HAR文件提取
        if har_file and os.path.exists(har_file):
            self._extract_from_har(har_file)

        # 自动检测当前目录的文件
        current_files = os.listdir('.')
        for file in current_files:
            if file.endswith('.txt') and 'request' in file:
                self._extract_from_txt(file)
            elif file.endswith('.har'):
                self._extract_from_har(file)

    def _extract_from_txt(self, txt_file: str):
        """从TXT文件提取信息"""
        try:
            with open(txt_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 提取act_token (原token字段)
            token_match = re.search(r'"token":\s*"([^"]+)"', content)
            if token_match and not self.act_token:
                self.act_token = token_match.group(1)
                logger.info(f"从 {txt_file} 提取到 act_token: {self.act_token[:10]}...")

            # 提取actKey
            act_key_match = re.search(r'"actKey":\s*"([^"]+)"', content)
            if act_key_match and not self.act_key:
                self.act_key = act_key_match.group(1)
                logger.info(f"从 {txt_file} 提取到 actKey: {self.act_key}")

            # 提取sdkToken
            sdk_token_match = re.search(r'"sdkToken":\s*"([^"]+)"', content)
            if sdk_token_match and not self.sdk_token:
                self.sdk_token = sdk_token_match.group(1)
                logger.info(f"从 {txt_file} 提取到 sdkToken: {self.sdk_token[:20]}...")

            # 提取rewardReceiveKey (如果存在)
            reward_key_match = re.search(r'"rewardReceiveKey":\s*"([^"]+)"', content)
            if reward_key_match and not self.reward_receive_key:
                self.reward_receive_key = reward_key_match.group(1)
                logger.info(f"从 {txt_file} 提取到 rewardReceiveKey: {self.reward_receive_key[:30]}...")

            # 提取rewardReceiveKey (如果存在)
            reward_key_match = re.search(r'"rewardReceiveKey":\s*"([^"]+)"', content)
            if reward_key_match and not self.reward_receive_key:
                self.reward_receive_key = reward_key_match.group(1)
                logger.info(f"从 {txt_file} 提取到 rewardReceiveKey: {self.reward_receive_key[:30]}...")

        except Exception as e:
            logger.warning(f"从 {txt_file} 提取信息失败: {e}")

    def _extract_from_har(self, har_file: str):
        """从HAR文件提取信息"""
        try:
            with open(har_file, 'r', encoding='utf-8') as f:
                har_data = json.load(f)

            for entry in har_data['log']['entries']:
                url = entry['request']['url']
                if 'api.m.jd.com' in url and 'client.action' in url:
                    # 提取Cookie
                    if not self.cookies:
                        headers = entry['request']['headers']
                        for header in headers:
                            if header['name'].lower() == 'cookie':
                                cookie_str = header['value']
                                for cookie in cookie_str.split(';'):
                                    cookie = cookie.strip()
                                    if '=' in cookie:
                                        key, value = cookie.split('=', 1)
                                        self.cookies[key.strip()] = value.strip()
                                self.session.cookies.update(self.cookies)
                                logger.info(f"从 {har_file} 提取到 Cookie 信息")
                                break

                    # 提取POST数据中的参数
                    if 'postData' in entry['request'] and 'text' in entry['request']['postData']:
                        post_data = entry['request']['postData']['text']
                        post_params = urllib.parse.parse_qs(post_data)

                        if 'body' in post_params:
                            try:
                                body_json = json.loads(post_params['body'][0])

                                if 'token' in body_json and not self.act_token:
                                    self.act_token = body_json['token']
                                    logger.info(f"从 {har_file} 提取到 act_token: {self.act_token[:10]}...")

                                if 'bizParams' in body_json:
                                    biz_params = body_json['bizParams']
                                    if 'actKey' in biz_params and not self.act_key:
                                        self.act_key = biz_params['actKey']
                                        logger.info(f"从 {har_file} 提取到 actKey: {self.act_key}")

                                if 'commParams' in body_json and 'sdkToken' in body_json['commParams'] and not self.sdk_token:
                                    self.sdk_token = body_json['commParams']['sdkToken']
                                    logger.info(f"从 {har_file} 提取到 sdkToken: {self.sdk_token[:20]}...")

                            except:
                                pass

        except Exception as e:
            logger.warning(f"从 {har_file} 提取信息失败: {e}")

    def _make_request(self, function_id: str, body_data: Dict[str, Any], retry_count: int = 0) -> Optional[Dict[str, Any]]:
        """
        发送请求的通用方法
        :param function_id: 接口ID
        :param body_data: 请求体数据
        :param retry_count: 重试次数
        :return: 响应数据
        """
        try:
            body_json = json.dumps(body_data, separators=(',', ':'))
            h5st = self.h5st_generator.generate(function_id, body_json)

            # 构建请求参数
            data = {
                'appid': 'day_day_reward',
                'functionId': function_id,
                'loginType': '2',
                'loginWQBiz': 'tttwxapp',
                'body': body_json,
                'h5st': h5st,
                'x-api-eid-token': self.cookies.get('3AB9D23F7A4B3CSS', '')
            }

            logger.debug(f"发送请求: {function_id}")

            response = self.session.post(
                'https://api.m.jd.com/client.action',
                data=data,
                timeout=30
            )
            response.raise_for_status()

            result = response.json()

            if result.get('success'):
                logger.debug(f"请求成功: {function_id}")
                return result.get('data')
            else:
                error_msg = result.get('message', '未知错误')
                logger.error(f"请求失败: {function_id} - {error_msg}")

                # 某些错误可以重试
                if retry_count < 3 and '网络' in error_msg:
                    logger.info(f"准备重试 ({retry_count + 1}/3)")
                    time.sleep(2)
                    return self._make_request(function_id, body_data, retry_count + 1)

                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"网络请求异常: {function_id} - {e}")

            if retry_count < 3:
                logger.info(f"网络异常，准备重试 ({retry_count + 1}/3)")
                time.sleep(2)
                return self._make_request(function_id, body_data, retry_count + 1)

            return None
        except Exception as e:
            logger.error(f"请求异常: {function_id} - {e}")
            return None

    def load_activity_data(self) -> bool:
        """
        加载活动数据 (对应 comp_data_load)
        这是第一步，从响应中获取所有必要参数
        """
        logger.info("🔄 正在加载活动数据...")

        if not self.act_token:
            logger.error("❌ 缺少必要的认证信息 (act_token)")
            logger.info("💡 请先设置基础认证信息或从文件自动提取")
            return False

        # 如果没有act_key，尝试使用默认值或从抓包文件提取
        if not self.act_key:
            logger.warning("⚠️ 缺少 act_key，尝试使用默认值")
            self.act_key = "iadem13cpdiof3vtyykmr"  # 从抓包分析得到的默认值

        body_data = {
            "token": self.act_token,
            "commParams": {
                "ubbLoc": "ttf.lqzx",
                "lid": "19_1601_50258_62859",
                "client": 0,
                "sdkToken": "jdd01BWS7QQMQLJE3EDTDUO4CW2RMKFTVHBIBDCDZCV7MYXYYSJ6YIRCACJRP3YL6QNJP3YNB2OQGMP7IOND55OYF67XM2O2YTDAHUDKVNEQ01234567"
            },
            "bizParams": {
                "openChannel": "jdAppHome",
                "actKey": self.act_key,
                "subLabel": ""
            }
        }

        data = self._make_request("comp_data_load", body_data)

        if data:
            self.activity_data = data
            logger.info("✅ 活动数据加载成功")

            # 提取关键信息
            self._extract_activity_info(data)
            return True
        else:
            logger.error("❌ 活动数据加载失败")
            return False

    def _extract_activity_info(self, data: Dict[str, Any]):
        """
        从第一步响应中提取所有必要的活动信息
        这是关键步骤，所有后续参数都从这里获取
        """
        try:
            logger.info("📝 正在从响应中提取关键参数...")

            # 提取活动基础信息
            act_basic_info = data.get('actBasicInfo', {})
            if act_basic_info:
                activity_name = act_basic_info.get('actName', '未知')
                start_time = act_basic_info.get('instanceBeginTime', '')
                end_time = act_basic_info.get('instanceEndTime', '')
                logger.info(f"📋 活动名称: {activity_name}")
                logger.info(f"⏰ 活动时间: {start_time} - {end_time}")

                # 从活动信息中提取actKey (如果响应中有的话)
                if 'actKey' in act_basic_info and act_basic_info['actKey']:
                    self.act_key = act_basic_info['actKey']
                    logger.info(f"🔑 从响应更新 actKey: {self.act_key}")

            # 提取奖励进度信息 - 这是获取rewardReceiveKey的关键
            reward_progress_items = data.get('rewardProgressItems', [])
            if reward_progress_items:
                logger.info(f"🎁 发现 {len(reward_progress_items)} 个奖励项目")
                for i, item in enumerate(reward_progress_items):
                    reward_key = item.get('rewardReceiveKey', '')
                    reward_name = item.get('rewardName', f'奖励{i+1}')
                    reward_status = item.get('rewardStatus', '未知状态')

                    logger.info(f"  {i+1}. {reward_name} - 状态: {reward_status}")

                    if reward_key and not self.reward_receive_key:
                        self.reward_receive_key = reward_key
                        logger.info(f"✅ 获取到 rewardReceiveKey: {reward_key[:30]}...")
                        break
            else:
                logger.warning("⚠️ 未找到奖励进度信息，可能活动已结束或无可领取奖励")

            # 提取用户身份信息
            identity_tag_map = data.get('identityTagMap', {})
            if identity_tag_map:
                logger.info(f"👤 用户身份标签: {list(identity_tag_map.keys())}")

            # 提取其他可能的关键信息
            if 'sdkToken' in data:
                new_sdk_token = data['sdkToken']
                if new_sdk_token != self.sdk_token:
                    self.sdk_token = new_sdk_token
                    logger.info(f"🔄 从响应更新 sdkToken: {new_sdk_token[:20]}...")

            # 记录提取结果
            logger.info("📊 参数提取结果:")
            logger.info(f"  - act_token: {'✅' if self.act_token else '❌'}")
            logger.info(f"  - act_key: {'✅' if self.act_key else '❌'}")
            logger.info(f"  - sdk_token: {'✅' if self.sdk_token else '❌'}")
            logger.info(f"  - reward_receive_key: {'✅' if self.reward_receive_key else '❌'}")

        except Exception as e:
            logger.warning(f"提取活动信息时出错: {e}")

    def claim_reward(self) -> bool:
        """
        领取奖励 (对应 comp_data_interact)
        """
        logger.info("🎁 正在领取奖励...")

        if not self.reward_receive_key:
            logger.error("❌ 缺少 rewardReceiveKey，请先加载活动数据")
            return False

        body_data = {
            "token": self.act_token,
            "fnCode": "invoke",
            "commParams": {
                "longitude": "113.328752",
                "latitude": "23.17921",
                "ubbLoc": "ttf.lqzx",
                "lid": "19_1601_50258_62859",
                "client": 0,
                "sdkToken": "jdd01BWS7QQMQLJE3EDTDUO4CW2RMKFTVHBIBDCDZCV7MYXYYSJ6YIRCACJRP3YL6QNJP3YNB2OQGMP7IOND55OYF67XM2O2YTDAHUDKVNEQ01234567"
            },
            "bizParams": {
                "rewardReceiveKey": self.reward_receive_key,
                "openChannel": "jdAppHome",
                "actFlowCode": "receiveReward",
                "actKey": self.act_key,
                "subLabel": ""
            }
        }

        data = self._make_request("comp_data_interact", body_data)

        if data:
            logger.info("🎉 奖励领取成功!")
            self._parse_reward_info(data)
            return True
        else:
            logger.error("❌ 奖励领取失败")
            return False

    def _parse_reward_info(self, data: Dict[str, Any]):
        """解析奖励信息"""
        try:
            reward_info_list = data.get('rewardInfoList', [])
            if not reward_info_list:
                logger.info("📦 本次没有获得奖励")
                return

            logger.info("🎁 奖励详情:")
            for i, reward in enumerate(reward_info_list, 1):
                coupon_info = reward.get('couponInfo', {})
                if coupon_info:
                    discount = coupon_info.get('couponDiscount', 0)
                    quota = coupon_info.get('couponQuota', 0)
                    limit_str = coupon_info.get('couponLimitStr', '')
                    valid_days = coupon_info.get('validDays', 0)

                    logger.info(f"  {i}. 🎫 {limit_str}")
                    logger.info(f"     💰 面额: {discount}元 (满{quota}元可用)")
                    logger.info(f"     ⏳ 有效期: {valid_days}天")
                else:
                    # 其他类型奖励
                    reward_type = reward.get('rewardType', '未知')
                    reward_name = reward.get('rewardName', '未知奖励')
                    logger.info(f"  {i}. 🎁 {reward_name} (类型: {reward_type})")

        except Exception as e:
            logger.warning(f"解析奖励信息时出错: {e}")

    def run_lottery(self) -> bool:
        """
        执行完整的抽奖流程
        """
        logger.info("🚀 开始执行抽奖流程...")

        # 检查基础认证信息
        if not self.act_token:
            logger.error("❌ 缺少必要的认证信息 (act_token)，请先设置或自动提取")
            return False

        # 第一步：加载活动数据
        if not self.load_activity_data():
            return False

        # 等待一下，模拟真实用户行为
        wait_time = random.uniform(1, 3)
        logger.info(f"⏳ 等待 {wait_time:.1f} 秒...")
        time.sleep(wait_time)

        # 第二步：领取奖励
        if not self.claim_reward():
            return False

        logger.info("✅ 抽奖流程执行完成!")
        return True

    def run_multiple_attempts(self, max_attempts: int = 5, interval: int = 60) -> int:
        """
        执行多次抽奖尝试
        :param max_attempts: 最大尝试次数
        :param interval: 尝试间隔(秒)
        :return: 成功次数
        """
        logger.info(f"🔄 开始执行多次抽奖，最多 {max_attempts} 次，间隔 {interval} 秒")

        success_count = 0

        for attempt in range(1, max_attempts + 1):
            logger.info(f"\n--- 第 {attempt}/{max_attempts} 次尝试 ---")

            if self.run_lottery():
                success_count += 1
                logger.info(f"🎉 第 {attempt} 次尝试成功!")
            else:
                logger.warning(f"❌ 第 {attempt} 次尝试失败")

            # 如果不是最后一次，等待间隔时间
            if attempt < max_attempts:
                logger.info(f"⏳ 等待 {interval} 秒后进行下一次尝试...")
                time.sleep(interval)

        logger.info(f"\n📊 抽奖结果统计: {success_count}/{max_attempts} 次成功")
        return success_count

    def check_activity_status(self) -> Dict[str, Any]:
        """
        检查活动状态
        """
        logger.info("🔍 检查活动状态...")

        if not self.load_activity_data():
            return {"status": "error", "message": "无法加载活动数据"}

        status_info = {
            "status": "success",
            "activity_name": "",
            "start_time": "",
            "end_time": "",
            "user_status": "",
            "available_rewards": []
        }

        try:
            # 活动基础信息
            act_basic_info = self.activity_data.get('actBasicInfo', {})
            status_info["activity_name"] = act_basic_info.get('actName', '未知活动')
            status_info["start_time"] = act_basic_info.get('instanceBeginTime', '')
            status_info["end_time"] = act_basic_info.get('instanceEndTime', '')

            # 用户状态
            identity_tag_map = self.activity_data.get('identityTagMap', {})
            if identity_tag_map:
                status_info["user_status"] = "已认证用户"
            else:
                status_info["user_status"] = "未认证用户"

            # 可用奖励
            reward_progress_items = self.activity_data.get('rewardProgressItems', [])
            for item in reward_progress_items:
                reward_info = {
                    "name": item.get('rewardName', '未知奖励'),
                    "status": item.get('rewardStatus', '未知状态'),
                    "key": item.get('rewardReceiveKey', '')[:20] + "..." if item.get('rewardReceiveKey') else ''
                }
                status_info["available_rewards"].append(reward_info)

        except Exception as e:
            logger.warning(f"解析活动状态时出错: {e}")
            status_info["status"] = "warning"
            status_info["message"] = f"部分信息解析失败: {e}"

        return status_info


def print_banner():
    """打印程序横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    京东外卖抽奖活动自动化脚本                      ║
║                     JD Takeaway Lottery Bot                   ║
║                                                              ║
║  基于抓包分析的完整实现 | 支持自动参数提取 | 智能错误处理          ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def interactive_setup() -> JDLotteryBot:
    """交互式设置"""
    print("\n🔧 交互式设置向导")
    print("=" * 50)

    bot = JDLotteryBot()

    # 选择认证信息来源
    print("\n请选择认证信息来源:")
    print("1. 手动输入")
    print("2. 从文件自动提取")
    print("3. 跳过设置(使用默认值)")

    choice = input("\n请输入选择 (1-3): ").strip()

    if choice == "1":
        # 手动输入
        print("\n📝 请输入认证信息:")
        cookies = input("Cookie字符串: ").strip()
        act_token = input("Act Token: ").strip()
        act_key = input("ActKey (可选，建议从响应获取): ").strip()
        sdk_token = input("SDK Token (可选，建议从响应获取): ").strip()

        bot.set_auth_info(cookies, act_token, act_key, sdk_token)

    elif choice == "2":
        # 自动提取
        print("\n🔍 正在从当前目录的文件中自动提取...")
        bot.auto_extract_from_files()

        if not bot.act_token:
            print("⚠️  自动提取失败，请手动输入关键信息:")
            if not bot.act_token:
                bot.act_token = input("Act Token: ").strip()

    elif choice == "3":
        print("⏭️  跳过设置，使用默认值")

    return bot


def main():
    """主函数"""
    print_banner()

    try:
        # 交互式设置
        bot = interactive_setup()

        # 选择运行模式
        print("\n🎯 请选择运行模式:")
        print("1. 单次抽奖")
        print("2. 多次抽奖")
        print("3. 检查活动状态")
        print("4. 测试连接")

        mode = input("\n请输入选择 (1-4): ").strip()

        if mode == "1":
            # 单次抽奖
            print("\n🎲 开始单次抽奖...")
            success = bot.run_lottery()
            if success:
                print("\n🎉 抽奖成功完成!")
            else:
                print("\n❌ 抽奖失败，请检查配置和网络")

        elif mode == "2":
            # 多次抽奖
            max_attempts = int(input("\n最大尝试次数 (默认5): ").strip() or "5")
            interval = int(input("尝试间隔秒数 (默认60): ").strip() or "60")

            success_count = bot.run_multiple_attempts(max_attempts, interval)
            print(f"\n📊 多次抽奖完成，成功 {success_count}/{max_attempts} 次")

        elif mode == "3":
            # 检查活动状态
            status = bot.check_activity_status()
            print("\n📋 活动状态信息:")
            print(json.dumps(status, indent=2, ensure_ascii=False))

        elif mode == "4":
            # 测试连接
            print("\n🔗 测试连接...")
            if bot.load_activity_data():
                print("✅ 连接测试成功!")
            else:
                print("❌ 连接测试失败!")

        else:
            print("❌ 无效选择")

    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        logger.error(f"程序运行异常: {e}")
        print(f"\n❌ 程序运行异常: {e}")


def demo_usage():
    """演示用法"""
    print("📚 演示用法:")
    print("""
# 基本用法
bot = JDLotteryBot()

# 设置认证信息
bot.set_auth_info(
    cookies="your_cookies_here",
    act_token="your_act_token_here",
    act_key="your_act_key_here"  # 可选，建议从响应获取
)

# 或者自动提取
bot.auto_extract_from_files()

# 执行抽奖
success = bot.run_lottery()

# 多次尝试
success_count = bot.run_multiple_attempts(max_attempts=5, interval=60)

# 检查状态
status = bot.check_activity_status()
    """)


if __name__ == "__main__":
    main()


"""
使用说明:

1. 安装依赖:
   pip install requests

2. 准备抓包文件:
   - 将 request(2).txt, request(3).txt 放在同目录
   - 或将 .har 文件放在同目录
   - 脚本会自动提取认证信息

3. 运行脚本:
   python jd_lottery_final.py

4. 按提示选择模式:
   - 单次抽奖: 执行一次完整流程
   - 多次抽奖: 循环执行多次
   - 检查状态: 查看活动信息
   - 测试连接: 验证配置是否正确

5. 配置文件:
   首次运行会生成 config.json 配置文件
   可以修改其中的参数来自定义行为

6. 日志文件:
   运行日志会保存到 jd_lottery.log 文件

7. 重要提醒:
   - h5st 参数使用简化算法，实际使用需要完整逆向
   - 请遵守相关服务条款，合理使用
   - 认证信息有时效性，需要定期更新
   - 建议在测试环境先验证功能

8. 文件结构:
   jd_lottery_final.py  # 主脚本
   config.json          # 配置文件(自动生成)
   jd_lottery.log       # 日志文件(自动生成)
   request(2).txt       # 抓包文件(可选)
   request(3).txt       # 抓包文件(可选)
   *.har               # HAR文件(可选)
"""